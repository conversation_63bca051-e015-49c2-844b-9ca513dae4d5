"""
状态机模块

该模块负责管理应用程序的状态转换逻辑，包括：
- 相机设置模式状态机
- 校准模式状态机  
- 检测模式状态机

主要组件：
- BaseStateMachine: 状态机基类
- CameraStateMachine: 相机设置模式状态机
- CalibrationStateMachine: 校准模式状态机
- DetectionStateMachine: 检测模式状态机
- StateMachineManager: 状态机管理器
"""

from .base_state_machine import BaseStateMachine, StateTransition
from .state_machine_manager import StateMachineManager

# 导入具体的状态机类
from .camera_state_machine import CameraStateMachine
from .calibration_state_machine import CalibrationStateMachine
from .detection_state_machine import DetectionStateMachine

__all__ = [
    'BaseStateMachine',
    'StateTransition',
    'StateMachineManager',
    'CameraStateMachine',
    'CalibrationStateMachine',
    'DetectionStateMachine',
]

__version__ = '1.0.0'
__author__ = 'UI Handler Refactor Team'
