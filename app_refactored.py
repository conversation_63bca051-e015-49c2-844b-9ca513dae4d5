"""
重构后的应用程序入口点

这是使用新模块化架构的应用程序主入口点。
它整合了所有重构后的模块，提供了一个清晰、可维护的应用程序结构。

主要特性：
- 使用模块化的状态机、UI组件和事件处理
- 保持与原有功能的完全兼容
- 提供更好的错误处理和日志记录
- 支持性能监控和统计
- 易于扩展和维护
"""

import sys
import os
import logging
import time

import traceback
from typing import Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
from app_state import AppState
from constants import *
import config_manager
import camera_manager

# 导入重构后的UI处理器
import ui_handler_refactored as ui_handler

# 导入线程模块
from capture_thread import CaptureWorker
from processing_thread import ProcessingWorker
from display_thread import DisplayWorker

# 导入其他必要模块
from roi_fine_tune import init_fine_tune_state
import base_point_manager


class RefactoredApplication:
    """重构后的应用程序主类"""
    
    def __init__(self):
        """初始化应用程序"""
        self.app_state: Optional[AppState] = None
        self.capture_thread_obj = None
        self.processing_thread_obj = None
        self.display_thread_obj = None
        self.running = False
        
        # 性能统计
        self.start_time = time.time()
        self.frame_count = 0
        self.last_stats_time = time.time()
        
        # 设置日志
        self._setup_logging()
        
        logging.info("RefactoredApplication initialized")
    
    def _setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('app_refactored.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # 设置第三方库的日志级别
        logging.getLogger('cv2').setLevel(logging.WARNING)
        logging.getLogger('numpy').setLevel(logging.WARNING)
    
    def initialize(self) -> bool:
        """初始化应用程序状态和组件"""
        try:
            logging.info("Initializing refactored application...")
            
            # 创建应用状态
            self.app_state = AppState()
            logging.info("AppState created")
            
            # 加载配置
            if not config_manager.load_config(self.app_state):
                logging.warning("Failed to load config, using defaults")
            else:
                logging.info("Configuration loaded successfully")
            
            # 初始化相机
            if not camera_manager.initialize_camera(self.app_state):
                logging.error("Failed to initialize camera")
                return False
            logging.info("Camera initialized successfully")
            
            # 初始化ROI微调状态
            init_fine_tune_state(self.app_state)
            logging.info("ROI fine-tune state initialized")
            
            # 初始化基准点管理器
            base_point_manager.reset_alignment_system(self.app_state)
            logging.info("Base point manager initialized")
            
            # 设置UI处理器的共享状态
            ui_handler.set_shared_state(self.app_state.shared_state)
            logging.info("UI handler shared state set")
            
            # 设置鼠标回调
            if not ui_handler.setup_mouse_callback(MAIN_WINDOW, self.app_state):
                logging.warning("Failed to setup mouse callback")
            else:
                logging.info("Mouse callback setup successfully")
            
            logging.info("Application initialization completed successfully")
            return True
            
        except Exception as e:
            logging.error(f"Failed to initialize application: {e}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def start_threads(self) -> bool:
        """启动所有工作线程"""
        try:
            logging.info("Starting application threads...")
            
            # 启动捕获线程
            self.capture_thread_obj = CaptureWorker(self.app_state)
            self.capture_thread_obj.start()
            logging.info("Capture thread started")

            # 启动处理线程
            self.processing_thread_obj = ProcessingWorker(self.app_state)
            self.processing_thread_obj.start()
            logging.info("Processing thread started")

            # 启动显示线程
            self.display_thread_obj = DisplayWorker(self.app_state.display_queue, self.app_state.shared_state)
            self.display_thread_obj.start()
            logging.info("Display thread started")
            
            self.running = True
            logging.info("All threads started successfully")
            return True
            
        except Exception as e:
            logging.error(f"Failed to start threads: {e}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def stop_threads(self):
        """停止所有工作线程"""
        try:
            logging.info("Stopping application threads...")
            
            # 设置停止标志
            if self.app_state:
                self.app_state.running = False
            
            # 停止线程
            workers_to_stop = [
                ("Capture", self.capture_thread_obj),
                ("Processing", self.processing_thread_obj),
                ("Display", self.display_thread_obj)
            ]

            for worker_name, worker_obj in workers_to_stop:
                if worker_obj:
                    logging.info(f"Stopping {worker_name} worker...")
                    worker_obj.stop()
                    logging.info(f"{worker_name} worker stopped")
            
            self.running = False
            logging.info("All threads stopped")
            
        except Exception as e:
            logging.error(f"Error stopping threads: {e}")
    
    def cleanup(self):
        """清理应用程序资源"""
        try:
            logging.info("Cleaning up application resources...")
            
            # 停止线程
            self.stop_threads()
            
            # 清理UI处理器
            ui_handler.cleanup()
            logging.info("UI handler cleaned up")
            
            # 清理相机
            if self.app_state:
                camera_manager.release_camera(self.app_state)
                logging.info("Camera released")
            
            # 保存配置
            if self.app_state:
                config_manager.save_config(self.app_state)
                logging.info("Configuration saved")
            
            logging.info("Application cleanup completed")
            
        except Exception as e:
            logging.error(f"Error during cleanup: {e}")
    
    def run(self) -> int:
        """运行应用程序主循环"""
        try:
            # 初始化应用程序
            if not self.initialize():
                logging.error("Application initialization failed")
                return 1
            
            # 启动线程
            if not self.start_threads():
                logging.error("Failed to start application threads")
                return 1
            
            logging.info("Application started successfully")
            logging.info("Press 'q' to quit, 'h' for help")
            
            # 主循环 - 监控应用程序状态
            last_stats_time = time.time()
            while self.running and self.app_state and self.app_state.running:
                try:
                    # 定期输出统计信息
                    current_time = time.time()
                    if current_time - last_stats_time > 30.0:  # 每30秒输出一次
                        self._print_performance_stats()
                        last_stats_time = current_time
                    
                    # 检查线程状态
                    self._check_thread_health()
                    
                    # 短暂休眠，避免占用过多CPU
                    time.sleep(0.1)
                    
                except KeyboardInterrupt:
                    logging.info("Keyboard interrupt received, shutting down...")
                    break
                except Exception as e:
                    logging.error(f"Error in main loop: {e}")
                    break
            
            logging.info("Application main loop ended")
            return 0
            
        except Exception as e:
            logging.error(f"Fatal error in application: {e}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            return 1
        
        finally:
            # 确保清理资源
            self.cleanup()
    
    def _check_thread_health(self):
        """检查线程健康状态"""
        workers_to_check = [
            ("Capture", self.capture_thread_obj),
            ("Processing", self.processing_thread_obj),
            ("Display", self.display_thread_obj)
        ]

        for worker_name, worker_obj in workers_to_check:
            if worker_obj and hasattr(worker_obj, 'thread') and worker_obj.thread and not worker_obj.thread.is_alive():
                logging.warning(f"{worker_name} worker has stopped unexpectedly")
                # 可以在这里添加重启线程的逻辑
    
    def _print_performance_stats(self):
        """打印性能统计信息"""
        try:
            current_time = time.time()
            uptime = current_time - self.start_time
            
            logging.info(f"=== Performance Stats (Uptime: {uptime:.1f}s) ===")
            
            if self.app_state:
                logging.info(f"Current Mode: {self.app_state.current_mode}")
                logging.info(f"FPS: {self.app_state.fps:.1f}")
                logging.info(f"Frame Count: {getattr(self.app_state, 'frame_count', 'N/A')}")
            
            # 获取UI处理器统计
            ui_stats = ui_handler.get_performance_stats()
            if ui_stats.get('initialized'):
                logging.info("UI Components Status: Initialized")
                
                # 状态机统计
                sm_stats = ui_stats.get('state_machine_manager')
                if sm_stats:
                    logging.info(f"State Machine: {sm_stats.get('current_state', 'Unknown')}")
                
                # 显示管理器统计
                dm_stats = ui_stats.get('display_manager')
                if dm_stats:
                    logging.info(f"Display Manager: {dm_stats.get('renderer_count', 0)} renderers")
                
                # 事件管理器统计
                em_stats = ui_stats.get('event_manager')
                if em_stats:
                    logging.info(f"Event Manager: {em_stats.get('total_events_processed', 0)} events processed")
            else:
                logging.info("UI Components Status: Not Initialized")
            
            logging.info("=" * 50)
            
        except Exception as e:
            logging.error(f"Error printing performance stats: {e}")


def main():
    """应用程序主入口点"""
    print("=" * 60)
    print("LED & Digit Detector - Refactored Version")
    print("Using modular architecture with state machines, UI components, and event handlers")
    print("=" * 60)
    
    # 创建并运行应用程序
    app = RefactoredApplication()
    exit_code = app.run()
    
    print("=" * 60)
    print("Application terminated")
    print("=" * 60)
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
