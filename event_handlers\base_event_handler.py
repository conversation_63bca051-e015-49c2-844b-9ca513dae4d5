"""
事件处理器基类

提供事件处理器的基础功能，包括事件过滤、状态检查、错误处理等。
所有具体的事件处理器都应该继承这个基类。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable
import time
import logging
from enum import Enum


class EventType(Enum):
    """事件类型枚举"""
    MOUSE_CLICK = "mouse_click"
    MOUSE_MOVE = "mouse_move"
    MOUSE_DRAG = "mouse_drag"
    MOUSE_RELEASE = "mouse_release"
    KEY_PRESS = "key_press"
    KEY_RELEASE = "key_release"
    CUSTOM = "custom"


class EventPriority(Enum):
    """事件优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class BaseEventHandler(ABC):
    """事件处理器基类"""
    
    def __init__(self, name: str = "BaseEventHandler"):
        """
        初始化事件处理器
        
        Args:
            name: 处理器名称，用于日志和调试
        """
        self.name = name
        self.enabled = True
        
        # 事件过滤配置
        self.supported_events: List[EventType] = []
        self.event_filters: Dict[EventType, Callable] = {}
        
        # 状态管理
        self.last_event_time = 0
        self.event_count = 0
        self.error_count = 0
        
        # 性能控制
        self.min_event_interval = 0.001  # 最小事件间隔（1ms）
        self.max_events_per_second = 1000  # 最大事件处理频率
        
        # 错误处理
        self.max_consecutive_errors = 5
        self.consecutive_errors = 0
        self.last_error_time = 0
        self.error_cooldown = 1.0  # 错误冷却时间（秒）
        
        # 调试和日志
        self.debug_mode = False
        self.log_events = False
        
        logging.debug(f"EventHandler '{self.name}' initialized")
    
    @abstractmethod
    def handle_event(self, event_type: EventType, event_data: Dict[str, Any], 
                    app_state: Any) -> bool:
        """
        处理事件
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
            app_state: 应用状态
            
        Returns:
            是否成功处理事件
        """
        pass
    
    def can_handle_event(self, event_type: EventType, app_state: Any) -> bool:
        """
        检查是否可以处理指定类型的事件
        
        Args:
            event_type: 事件类型
            app_state: 应用状态
            
        Returns:
            是否可以处理
        """
        if not self.enabled:
            return False
        
        if event_type not in self.supported_events:
            return False
        
        # 检查错误状态
        if self.consecutive_errors >= self.max_consecutive_errors:
            current_time = time.time()
            if (current_time - self.last_error_time) < self.error_cooldown:
                return False
            else:
                # 重置错误计数
                self.consecutive_errors = 0
        
        # 检查事件频率
        current_time = time.time()
        if (current_time - self.last_event_time) < self.min_event_interval:
            return False
        
        # 应用事件过滤器
        if event_type in self.event_filters:
            filter_func = self.event_filters[event_type]
            try:
                return filter_func(event_type, app_state)
            except Exception as e:
                logging.error(f"Event filter error in '{self.name}': {e}")
                return False
        
        return True
    
    def process_event(self, event_type: EventType, event_data: Dict[str, Any], 
                     app_state: Any) -> bool:
        """
        处理事件（带错误处理和性能统计）
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
            app_state: 应用状态
            
        Returns:
            是否成功处理事件
        """
        if not self.can_handle_event(event_type, app_state):
            return False
        
        current_time = time.time()
        
        try:
            # 记录事件
            if self.log_events:
                logging.debug(f"Handler '{self.name}' processing {event_type.value}")
            
            # 处理事件
            result = self.handle_event(event_type, event_data, app_state)
            
            # 更新统计
            self.last_event_time = current_time
            self.event_count += 1
            
            # 重置错误计数（成功处理）
            if result:
                self.consecutive_errors = 0
            
            return result
            
        except Exception as e:
            # 错误处理
            self.consecutive_errors += 1
            self.error_count += 1
            self.last_error_time = current_time
            
            logging.error(f"Event handler '{self.name}' failed: {e}")
            
            if self.debug_mode:
                import traceback
                logging.debug(f"Event handler traceback:\n{traceback.format_exc()}")
            
            return False
    
    def add_event_filter(self, event_type: EventType, filter_func: Callable) -> bool:
        """
        添加事件过滤器
        
        Args:
            event_type: 事件类型
            filter_func: 过滤函数，接受(event_type, app_state)参数，返回bool
            
        Returns:
            是否成功添加
        """
        if not callable(filter_func):
            logging.error(f"Filter function must be callable")
            return False
        
        self.event_filters[event_type] = filter_func
        logging.debug(f"Added event filter for {event_type.value} in '{self.name}'")
        return True
    
    def remove_event_filter(self, event_type: EventType) -> bool:
        """
        移除事件过滤器
        
        Args:
            event_type: 事件类型
            
        Returns:
            是否成功移除
        """
        if event_type in self.event_filters:
            del self.event_filters[event_type]
            logging.debug(f"Removed event filter for {event_type.value} in '{self.name}'")
            return True
        return False
    
    def set_enabled(self, enabled: bool):
        """设置是否启用"""
        if self.enabled != enabled:
            self.enabled = enabled
            if enabled:
                logging.debug(f"EventHandler '{self.name}' enabled")
                # 重置错误状态
                self.consecutive_errors = 0
            else:
                logging.debug(f"EventHandler '{self.name}' disabled")
    
    def set_event_interval(self, interval: float):
        """
        设置最小事件间隔
        
        Args:
            interval: 最小间隔（秒）
        """
        self.min_event_interval = max(0.0001, interval)  # 最小0.1ms间隔
        logging.debug(f"Handler '{self.name}' event interval set to {self.min_event_interval:.4f}s")
    
    def set_max_events_per_second(self, max_events: int):
        """
        设置最大事件处理频率
        
        Args:
            max_events: 每秒最大事件数
        """
        self.max_events_per_second = max(1, max_events)
        self.min_event_interval = 1.0 / self.max_events_per_second
        logging.debug(f"Handler '{self.name}' max events per second set to {max_events}")
    
    def reset_error_state(self):
        """重置错误状态"""
        self.consecutive_errors = 0
        self.error_count = 0
        self.last_error_time = 0
        logging.debug(f"Handler '{self.name}' error state reset")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            性能统计字典
        """
        current_time = time.time()
        uptime = current_time - (self.last_event_time - self.event_count * self.min_event_interval 
                                if self.event_count > 0 else current_time)
        
        events_per_second = self.event_count / uptime if uptime > 0 else 0
        error_rate = self.error_count / self.event_count if self.event_count > 0 else 0
        
        return {
            'name': self.name,
            'enabled': self.enabled,
            'event_count': self.event_count,
            'error_count': self.error_count,
            'consecutive_errors': self.consecutive_errors,
            'events_per_second': events_per_second,
            'error_rate': error_rate,
            'last_event_time': self.last_event_time,
            'supported_events': [e.value for e in self.supported_events],
            'filter_count': len(self.event_filters),
            'min_event_interval': self.min_event_interval,
            'max_events_per_second': self.max_events_per_second
        }
    
    def reset_performance_stats(self):
        """重置性能统计"""
        self.event_count = 0
        self.error_count = 0
        self.last_event_time = 0
        logging.debug(f"Handler '{self.name}' performance stats reset")
    
    def set_debug_mode(self, debug: bool):
        """设置调试模式"""
        self.debug_mode = debug
        logging.debug(f"Handler '{self.name}' debug mode: {debug}")
    
    def set_log_events(self, log_events: bool):
        """设置是否记录事件日志"""
        self.log_events = log_events
        logging.debug(f"Handler '{self.name}' event logging: {log_events}")
    
    def validate_event_data(self, event_type: EventType, event_data: Dict[str, Any]) -> bool:
        """
        验证事件数据的有效性
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
            
        Returns:
            数据是否有效
        """
        if not isinstance(event_data, dict):
            return False
        
        # 基本验证 - 子类可以重写以添加特定验证
        return True
    
    def cleanup(self):
        """清理资源"""
        self.event_filters.clear()
        self.reset_performance_stats()
        self.reset_error_state()
        logging.debug(f"EventHandler '{self.name}' cleaned up")

    def get_supported_events(self) -> List[EventType]:
        """获取支持的事件类型列表"""
        return self.supported_events.copy()

    def add_supported_event(self, event_type: EventType):
        """添加支持的事件类型"""
        if event_type not in self.supported_events:
            self.supported_events.append(event_type)
            logging.debug(f"Added supported event {event_type.value} to '{self.name}'")

    def remove_supported_event(self, event_type: EventType):
        """移除支持的事件类型"""
        if event_type in self.supported_events:
            self.supported_events.remove(event_type)
            logging.debug(f"Removed supported event {event_type.value} from '{self.name}'")
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"EventHandler(name='{self.name}', enabled={self.enabled})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        stats = self.get_performance_stats()
        return (f"EventHandler(name='{self.name}', enabled={self.enabled}, "
                f"events={stats['event_count']}, errors={stats['error_count']})")
