"""
检测模式状态机

负责处理检测模式的状态转换和逻辑处理，包括：
- LED检测
- 数码管检测
- "88"触发的分析流程状态机
- "1P"触发的信号发送
- 基准点自动对齐
"""

import cv2
import numpy as np
import time
import logging
import os
import sys

from .base_state_machine import BaseStateMachine, StateTransition
from constants import *
import led_detector
import digit_detector
import base_point_manager
import async_task_manager
from async_task_manager import TaskType, TaskStatus


class DetectionStateMachine(BaseStateMachine):
    """检测模式状态机"""
    
    # 检测模式的主要状态
    STATE_DETECTION_RUNNING = "DETECTION_RUNNING"
    STATE_DETECTION_ERROR = "DETECTION_ERROR"
    
    # "88"分析流程的子状态（对应原来的led_analysis_state）
    ANALYSIS_IDLE = 'IDLE'
    ANALYSIS_LOGGING = 'LOGGING'
    ANALYSIS_WAITING_TO_SIGNAL_12 = 'WAITING_TO_SIGNAL_12'
    ANALYSIS_ANALYZING = 'ANALYZING'
    ANALYSIS_SENDING_RESULT_10 = 'SENDING_RESULT_10'
    ANALYSIS_CLEARING = 'CLEARING'
    
    def __init__(self):
        """初始化检测状态机"""
        super().__init__(self.STATE_DETECTION_RUNNING, "DetectionStateMachine")
        
        # 分析状态机的当前状态
        self.analysis_state = self.ANALYSIS_IDLE
        
        # 检测相关的状态
        self.frame_read_failed = False
        self.last_digit_display = ""
        self.last_key = -1
    
    def setup_transitions(self):
        """设置状态转换规则"""
        # 检测运行 -> 错误（相机失败）
        self.add_transition(StateTransition(
            self.STATE_DETECTION_RUNNING,
            self.STATE_DETECTION_ERROR,
            condition=self._camera_failed
        ))
        
        # 错误 -> 检测运行（相机恢复）
        self.add_transition(StateTransition(
            self.STATE_DETECTION_ERROR,
            self.STATE_DETECTION_RUNNING,
            condition=self._camera_recovered
        ))
    
    def setup_handlers(self):
        """设置状态处理器"""
        self.add_state_handler(self.STATE_DETECTION_RUNNING, self._handle_detection_running)
        self.add_state_handler(self.STATE_DETECTION_ERROR, self._handle_detection_error)
    
    def process_state(self, app_state) -> bool:
        """处理当前状态（重写基类方法以添加检测特定逻辑）"""
        # 获取按键
        import ui_handler_refactored as ui_handler
        self.last_key = ui_handler.get_key()
        
        # 调用基类的状态处理
        result = super().process_state(app_state)
        
        # 如果在检测运行状态，处理分析状态机
        if self.current_state == self.STATE_DETECTION_RUNNING:
            self._process_analysis_state_machine(app_state)
        
        return result
    
    def _camera_failed(self, app_state) -> bool:
        """检查相机是否失败"""
        return (app_state.cap is None or 
                not app_state.cap.isOpened() or 
                self.frame_read_failed)
    
    def _camera_recovered(self, app_state) -> bool:
        """检查相机是否恢复"""
        return (app_state.cap is not None and 
                app_state.cap.isOpened() and 
                not self.frame_read_failed)
    
    def _handle_detection_running(self, app_state) -> bool:
        """处理检测运行状态"""
        # 检查相机状态
        if self._camera_failed(app_state):
            self.transition_to(self.STATE_DETECTION_ERROR, app_state)
            return True
        
        # 获取当前帧
        frame = self._get_current_frame(app_state)
        if frame is None:
            self.frame_read_failed = True
            return True
        
        self.frame_read_failed = False
        
        # 准备帧数据
        original_frame = frame.copy()
        app_state.display_frame = frame.copy()
        app_state.current_frame = original_frame
        
        # 预计算灰度图
        try:
            app_state.gray_frame = cv2.cvtColor(original_frame, cv2.COLOR_BGR2GRAY)
        except Exception:
            app_state.gray_frame = None
        
        # ROI自动对齐
        self._perform_roi_alignment(app_state, original_frame)
        
        # LED检测
        self._perform_led_detection(app_state, original_frame)
        
        # 数码管检测
        self._perform_digit_detection(app_state, original_frame)
        
        # 处理按键输入
        self._handle_detection_input(app_state)
        
        return True
    
    def _handle_detection_error(self, app_state) -> bool:
        """处理检测错误状态"""
        app_state.prompt_message = "Error: Camera not initialized for detection!"
        app_state.status_message = "Check camera connection"
        
        # 创建错误显示帧
        h, w = (480, 640)
        if app_state.display_frame is not None:
            h, w = app_state.display_frame.shape[:2]
        app_state.display_frame = np.zeros((h, w, 3), dtype=np.uint8)
        
        # 检查相机是否恢复
        if self._camera_recovered(app_state):
            self.transition_to(self.STATE_DETECTION_RUNNING, app_state)
        
        return True
    
    def _get_current_frame(self, app_state):
        """获取当前帧"""
        # 优先从采集线程获取最新帧
        frame = None
        if hasattr(app_state, 'frame_queue') and not app_state.frame_queue.empty():
            try:
                frame = app_state.frame_queue.get_nowait()
                while True:
                    frame = app_state.frame_queue.get_nowait()
            except Exception:
                pass
        
        # 若没有采集线程帧，则直接读取相机
        if frame is None:
            try:
                ret, frame = app_state.cap.read()
                if not ret or frame is None:
                    return None
            except Exception:
                return None
        
        return frame
    
    def _perform_roi_alignment(self, app_state, frame):
        """执行ROI自动对齐"""
        try:
            alignment_success = base_point_manager.auto_align_rois(app_state, frame)
            
            # 更新对齐状态显示
            if app_state.alignment_enabled:
                if alignment_success:
                    if app_state.alignment_fail_count == 0:
                        alignment_status = " | Base Alignment: True"
                    else:
                        alignment_status = f" | Base Alignment: Recovering ({app_state.alignment_fail_count})"
                else:
                    alignment_status = f" | Base Alignment: False ({app_state.alignment_fail_count})"
            else:
                alignment_status = " | Base Alignment: Disabled"
            
            # 将对齐状态添加到状态消息中
            if not hasattr(app_state, '_alignment_status'):
                app_state._alignment_status = alignment_status
            else:
                app_state._alignment_status = alignment_status
                
        except Exception as e:
            logging.error(f"ROI alignment failed: {e}")
    
    def _perform_led_detection(self, app_state, frame):
        """执行LED检测"""
        try:
            led_detector.detect_led_status(frame, app_state)
        except Exception as e:
            logging.error(f"LED detection failed: {e}")
    
    def _perform_digit_detection(self, app_state, frame):
        """执行数码管检测"""
        try:
            recognized_chars, segment_patterns, _ = digit_detector.detect_digit_status(frame, app_state)
            
            # 组合识别结果
            current_display = ""
            if len(recognized_chars) >= 2:
                char1 = recognized_chars[0] if recognized_chars[0] is not None else ''
                char2 = recognized_chars[1] if recognized_chars[1] is not None else ''
                current_display = f"{char1}{char2}"
                
                # 添加详细调试日志
                logging.debug(f"数码管识别结果: 左侧='{char1}', 右侧='{char2}', 组合='{current_display}'")
                logging.debug(f"左侧段模式: {segment_patterns[0]}, 右侧段模式: {segment_patterns[1]}")
            
            self.last_digit_display = current_display
            
        except Exception as e:
            logging.error(f"Digit detection failed: {e}")
            self.last_digit_display = ""
    
    def _handle_detection_input(self, app_state):
        """处理检测模式的按键输入"""
        if self.last_key == ord('q'):
            app_state.running = False
        elif self.last_key == ord('c'):
            # 切换到校准模式
            app_state._mode_switch_requested = MODE_CALIBRATION
            app_state.current_calib_state = CALIB_STATE_START
        elif self.last_key == ord('a'):
            # 切换对齐功能
            app_state.alignment_enabled = not app_state.alignment_enabled
            logging.info(f"Base point alignment {'enabled' if app_state.alignment_enabled else 'disabled'}")
    
    def _process_analysis_state_machine(self, app_state):
        """处理"88"分析状态机"""
        current_time = time.time()
        
        if self.analysis_state == self.ANALYSIS_IDLE:
            self._handle_analysis_idle(app_state, current_time)
        elif self.analysis_state == self.ANALYSIS_LOGGING:
            self._handle_analysis_logging(app_state, current_time)
        elif self.analysis_state == self.ANALYSIS_WAITING_TO_SIGNAL_12:
            self._handle_analysis_waiting_signal_12(app_state)
        elif self.analysis_state == self.ANALYSIS_ANALYZING:
            self._handle_analysis_analyzing(app_state)
        elif self.analysis_state == self.ANALYSIS_SENDING_RESULT_10:
            self._handle_analysis_sending_result(app_state)
        elif self.analysis_state == self.ANALYSIS_CLEARING:
            self._handle_analysis_clearing(app_state)
    
    def _handle_analysis_idle(self, app_state, current_time):
        """处理分析空闲状态"""
        # 检查"88"触发
        if self.last_digit_display == "88":
            if app_state.log_file_handler:
                logging.info(f"Detected '88', starting {app_state.logging_duration}s logging process.")
                try:
                    # 清空旧日志文件
                    with open(LOG_FILE, 'w', encoding='utf-8') as f:
                        pass
                    logging.info(f"Log file '{LOG_FILE}' cleared before starting.")
                    
                    logger = logging.getLogger()
                    logger.addHandler(app_state.log_file_handler)
                    logging.info("FileHandler added to logger.")
                    
                    app_state.logging_start_time = current_time
                    self.analysis_state = self.ANALYSIS_LOGGING
                    
                except Exception as e:
                    logging.error(f"Error starting log file: {e}")
            else:
                logging.error("Cannot start logging for '88': Log file handler not initialized.")
        
        # 检查"1P"触发
        elif self.last_digit_display in ["IP", "1P", "iP", "Ip"]:
            trigger_text = self.last_digit_display
            logging.info(f"Detected '{trigger_text}', submitting async signal to CPU address 11.")
            
            # 异步发送IP信号
            task_manager = async_task_manager.get_task_manager()
            task_id = task_manager.submit_task(
                TaskType.SEND_CPU_SIGNAL,
                {'value': 1, 'address': 11}
            )
            logging.info(f"Submitted IP signal task: {task_id}")
    
    def _handle_analysis_logging(self, app_state, current_time):
        """处理分析日志记录状态"""
        elapsed_time = current_time - app_state.logging_start_time
        app_state.status_message = f"Logging for '88' analysis... {elapsed_time:.1f}s / {app_state.logging_duration}s"
        
        if elapsed_time >= app_state.logging_duration:
            logging.info(f"{app_state.logging_duration}s logging finished. Stopping file handler.")
            if app_state.log_file_handler:
                try:
                    logger = logging.getLogger()
                    logger.removeHandler(app_state.log_file_handler)
                    logging.info("FileHandler removed from logger.")
                except Exception as e:
                    logging.error(f"Error removing log handler: {e}")
            
            self.analysis_state = self.ANALYSIS_WAITING_TO_SIGNAL_12
    
    def _handle_analysis_waiting_signal_12(self, app_state):
        """处理等待发送信号12状态"""
        # 如果还没有提交CPU通信任务，则提交
        if app_state.current_cpu_task_id is None:
            logging.info("Submitting CPU task: send 1 to addr 12")
            
            task_manager = async_task_manager.get_task_manager()
            task_id = task_manager.submit_task(
                TaskType.SEND_CPU_SIGNAL,
                {'value': 1, 'address': 12}
            )
            app_state.current_cpu_task_id = task_id
            app_state.async_task_progress = "Sending log completion signal to CPU..."
        
        # 检查CPU通信任务状态
        task_manager = async_task_manager.get_task_manager()
        task_status = task_manager.get_task_status(app_state.current_cpu_task_id)
        
        if task_status == TaskStatus.RUNNING:
            app_state.status_message = "Sending log completion signal to CPU..."
        elif task_status == TaskStatus.COMPLETED:
            result = task_manager.get_task_result(app_state.current_cpu_task_id)
            if result:
                self.analysis_state = self.ANALYSIS_ANALYZING
                logging.info("Signal 1 to addr 12 sent successfully. Starting analysis.")
                app_state.current_cpu_task_id = None
                app_state.async_task_progress = ""
            else:
                logging.error("Failed to send signal (1 to addr 12). Retrying.")
                app_state.current_cpu_task_id = None
        elif task_status == TaskStatus.FAILED:
            error_msg = task_manager.get_task_error(app_state.current_cpu_task_id)
            logging.error(f"CPU communication task failed: {error_msg}")
            app_state.current_cpu_task_id = None
    
    def _handle_analysis_analyzing(self, app_state):
        """处理分析执行状态"""
        # 如果还没有提交分析任务，则提交
        if app_state.current_analysis_task_id is None:
            try:
                # 构造正确的日志文件路径
                if getattr(sys, 'frozen', False):
                    log_dir = os.path.dirname(sys.executable)
                else:
                    log_dir = os.path.dirname(os.path.abspath(__file__))
                
                log_file_path = os.path.join(log_dir, LOG_FILE)
                
                logging.info(f"Analysis log file path: {log_file_path}")
                logging.info(f"Log file exists: {os.path.exists(log_file_path)}")
                if os.path.exists(log_file_path):
                    file_size = os.path.getsize(log_file_path)
                    logging.info(f"Log file size: {file_size} bytes")
                
                # 提交异步分析任务
                task_manager = async_task_manager.get_task_manager()
                task_id = task_manager.submit_task(
                    TaskType.ANALYZE_LOG,
                    {'log_file_path': log_file_path}
                )
                app_state.current_analysis_task_id = task_id
                app_state.async_task_progress = "Analyzing log file..."
                logging.info(f"Submitted analysis task: {task_id}")
                
            except Exception as e:
                logging.error(f"Error submitting analysis task: {e}")
                self.analysis_state = self.ANALYSIS_CLEARING
                return
        
        # 检查分析任务状态
        task_manager = async_task_manager.get_task_manager()
        task_status = task_manager.get_task_status(app_state.current_analysis_task_id)
        
        if task_status == TaskStatus.RUNNING:
            app_state.status_message = "Analyzing log file, please wait..."
            app_state.async_task_progress = "Analysis in progress..."
        elif task_status == TaskStatus.COMPLETED:
            # 获取分析结果
            analysis_result = task_manager.get_task_result(app_state.current_analysis_task_id)
            
            if analysis_result:
                found_cycles = analysis_result['perfect_cycles']
                special_leds_status = analysis_result['special_leds_status']
                
                app_state.last_analysis_result = found_cycles
                app_state.last_special_leds_result = special_leds_status
                
                logging.info(f"Analysis complete. Found {found_cycles} perfect cycles.")
                logging.info(f"Special LEDs (G33/R1/R2) status: {special_leds_status}")
                
                # 记录详细的G33/R1/R2分析结果
                g33_result = analysis_result['g33_result']
                r1_result = analysis_result['r1_result']
                r2_result = analysis_result['r2_result']
                
                logging.info(f"G33: {g33_result['total_duration']:.3f}s ({'GOOD' if g33_result['is_good'] else 'BAD'})")
                logging.info(f"R1: {r1_result['total_duration']:.3f}s ({'GOOD' if r1_result['is_good'] else 'BAD'})")
                logging.info(f"R2: {r2_result['total_duration']:.3f}s ({'GOOD' if r2_result['is_good'] else 'BAD'})")
                
                self.analysis_state = self.ANALYSIS_SENDING_RESULT_10
                app_state.current_analysis_task_id = None
                app_state.async_task_progress = ""
            else:
                logging.error("Analysis function returned None or empty result.")
                self.analysis_state = self.ANALYSIS_CLEARING
                app_state.current_analysis_task_id = None
                app_state.async_task_progress = ""
        elif task_status == TaskStatus.FAILED:
            error_msg = task_manager.get_task_error(app_state.current_analysis_task_id)
            logging.error(f"Analysis task failed: {error_msg}")
            self.analysis_state = self.ANALYSIS_CLEARING
            app_state.current_analysis_task_id = None
            app_state.async_task_progress = ""
    
    def _handle_analysis_sending_result(self, app_state):
        """处理发送分析结果状态"""
        if app_state.last_analysis_result is not None:
            # 如果还没有提交CPU通信任务，则提交
            if app_state.current_cpu_task_id is None:
                value_to_send = 3 if app_state.last_analysis_result == 0 else 1
                logging.info(f"Submitting CPU task: send {value_to_send} to addr 10")
                
                task_manager = async_task_manager.get_task_manager()
                task_id = task_manager.submit_task(
                    TaskType.SEND_CPU_SIGNAL,
                    {'value': value_to_send, 'address': 10}
                )
                app_state.current_cpu_task_id = task_id
                app_state.async_task_progress = f"Sending result {value_to_send} to CPU..."
            
            # 检查CPU通信任务状态
            task_manager = async_task_manager.get_task_manager()
            task_status = task_manager.get_task_status(app_state.current_cpu_task_id)
            
            if task_status == TaskStatus.RUNNING:
                app_state.status_message = f"Sending analysis result to CPU..."
            elif task_status == TaskStatus.COMPLETED:
                result = task_manager.get_task_result(app_state.current_cpu_task_id)
                if result:
                    logging.info("Analysis result sent successfully. Clearing state.")
                    self.analysis_state = self.ANALYSIS_CLEARING
                    app_state.current_cpu_task_id = None
                    app_state.async_task_progress = ""
                else:
                    logging.error("Failed to send analysis result. Retrying.")
                    app_state.current_cpu_task_id = None
            elif task_status == TaskStatus.FAILED:
                error_msg = task_manager.get_task_error(app_state.current_cpu_task_id)
                logging.error(f"CPU communication task failed: {error_msg}")
                app_state.current_cpu_task_id = None
    
    def _handle_analysis_clearing(self, app_state):
        """处理分析清理状态"""
        # 清理分析相关的状态
        app_state.last_analysis_result = None
        app_state.last_special_leds_result = None
        app_state.current_analysis_task_id = None
        app_state.current_cpu_task_id = None
        app_state.async_task_progress = ""
        
        # 返回空闲状态
        self.analysis_state = self.ANALYSIS_IDLE
        logging.info("Analysis state machine cleared, returning to IDLE.")
    
    def can_exit_mode(self, app_state) -> bool:
        """检查是否可以退出检测模式"""
        # 如果正在进行分析，可能需要等待完成
        if self.analysis_state in [self.ANALYSIS_LOGGING, self.ANALYSIS_ANALYZING]:
            return False
        return True
    
    def on_mode_enter(self, app_state):
        """进入检测模式时的处理"""
        logging.info("Entered Detection mode")
        # 重置到检测运行状态
        self.reset_to_initial(self.STATE_DETECTION_RUNNING)
        # 重置分析状态机
        self.analysis_state = self.ANALYSIS_IDLE
        # 更新app_state中的分析状态
        app_state.led_analysis_state = self.ANALYSIS_IDLE
    
    def on_mode_exit(self, app_state):
        """退出检测模式时的处理"""
        logging.info("Exited Detection mode")
        # 清理状态
        self.frame_read_failed = False
        self.last_digit_display = ""
        self.last_key = -1
