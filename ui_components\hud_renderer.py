"""
HUD渲染器

负责绘制所有HUD（Head-Up Display）元素，包括：
- 状态信息（LED状态、数码管状态）
- 系统信息（FPS、模式、置顶状态）
- 提示信息（操作提示、错误信息）
- 阈值信息（LED阈值、数码管阈值）
"""

import cv2
import numpy as np
import time
from typing import Any, Dict, List, Tuple
import logging

from .base_renderer import BaseRenderer
from constants import *


class HUDRenderer(BaseRenderer):
    """HUD渲染器"""
    
    def __init__(self):
        """初始化HUD渲染器"""
        super().__init__("HUDRenderer")
        
        # HUD颜色配置
        self.colors = {
            'info': (255, 255, 0),        # 信息文本（黄色）
            'led_on_green': (0, 255, 0),  # 绿色LED开启
            'led_on_red': (0, 0, 255),    # 红色LED开启
            'led_off': (150, 150, 150),   # LED关闭（灰色）
            'led_normal': (255, 255, 255), # LED正常状态（白色）
            'digit_ok': (0, 255, 0),      # 数码管正常（绿色）
            'digit_unknown': (0, 165, 255), # 数码管未知（橙色）
            'digit_fail': (0, 0, 255),    # 数码管失败（红色）
            'fps': (0, 255, 0),           # FPS（绿色）
            'mode': (255, 255, 0),        # 模式（黄色）
            'topmost_on': (0, 255, 0),    # 置顶开启（绿色）
            'topmost_off': (128, 128, 128), # 置顶关闭（灰色）
            'status': (0, 255, 255),      # 状态消息（青色）
            'prompt': (255, 255, 255),    # 提示消息（白色）
            'progress': (255, 165, 0),    # 进度信息（橙色）
        }
        
        # 字体配置
        self.base_font_scale = 0.32
        self.line_height = 15
        
        # HUD刷新控制
        self.hud_refresh_interval = 0.1  # 10Hz刷新率
        self.last_hud_update = 0
        self.hud_canvas = None
        self.hud_mask = None
        
        # 设置更新间隔为HUD刷新间隔
        self.set_update_interval(self.hud_refresh_interval)
    
    def render(self, frame: np.ndarray, app_state: Any) -> np.ndarray:
        """
        渲染HUD到帧上
        
        Args:
            frame: 输入帧
            app_state: 应用状态
            
        Returns:
            渲染后的帧
        """
        if not self.validate_frame(frame):
            return frame
        
        if app_state.display_frame is None:
            return frame
        
        h, w = app_state.display_frame.shape[:2]
        
        # 计算字体大小
        font_scale, led_font_scale = self._calculate_font_scales(w)
        
        # 检查是否需要刷新HUD
        current_time = time.time()
        need_refresh = self._need_hud_refresh(h, w, current_time, app_state)
        
        if need_refresh:
            # 重新生成HUD图层
            self._generate_hud_layer(h, w, font_scale, led_font_scale, app_state)
            self.last_hud_update = current_time
            
            # 缓存HUD
            app_state.hud_canvas = self.hud_canvas.copy() if self.hud_canvas is not None else None
            app_state.hud_mask = self.hud_mask.copy() if self.hud_mask is not None else None
            app_state.hud_last_update_ts = current_time
        else:
            # 使用缓存的HUD
            self.hud_canvas = app_state.hud_canvas
            self.hud_mask = app_state.hud_mask
        
        # 将HUD叠加到显示帧上
        if self.hud_canvas is not None and self.hud_mask is not None:
            self._overlay_hud(app_state.display_frame, self.hud_canvas, self.hud_mask)
        
        return app_state.display_frame
    
    def _calculate_font_scales(self, width: int) -> Tuple[float, float]:
        """计算字体大小"""
        base_font_scale = self.base_font_scale
        
        if width > 1600:  # 高分辨率时稍微增大
            base_font_scale = 0.36
        elif width < 1000:  # 低分辨率时进一步减小
            base_font_scale = 0.28
        
        font_scale = base_font_scale
        led_font_scale = base_font_scale - 0.05  # LED状态文本稍小
        
        return font_scale, led_font_scale
    
    def _need_hud_refresh(self, h: int, w: int, current_time: float, app_state: Any) -> bool:
        """检查是否需要刷新HUD"""
        return (
            self.hud_canvas is None or
            self.hud_mask is None or
            (self.hud_canvas.shape[:2] != (h, w)) or
            (current_time - self.last_hud_update) >= self.hud_refresh_interval or
            getattr(app_state, 'hud_canvas', None) is None or
            getattr(app_state, 'hud_mask', None) is None or
            (getattr(app_state, 'hud_canvas', np.array([])).shape[:2] != (h, w))
        )
    
    def _generate_hud_layer(self, h: int, w: int, font_scale: float, 
                           led_font_scale: float, app_state: Any):
        """生成HUD图层"""
        # 创建透明HUD图层
        self.hud_canvas = np.zeros((h, w, 3), dtype=np.uint8)
        self.hud_mask = np.zeros((h, w), dtype=np.uint8)
        
        y_offset = 15
        
        # 绘制数码管信息
        if app_state.current_mode == MODE_DETECTION:
            y_offset = self._draw_digit_info(y_offset, font_scale, led_font_scale, app_state)
            y_offset = self._draw_led_info(y_offset, font_scale, led_font_scale, app_state)
        
        # 绘制右上角信息
        self._draw_mode_and_fps(w, font_scale, app_state)
        
        # 绘制底部信息
        self._draw_bottom_info(h, led_font_scale, app_state)
        
        # 构建掩膜
        self._build_hud_mask()
    
    def _draw_digit_info(self, y_offset: int, font_scale: float, 
                        led_font_scale: float, app_state: Any) -> int:
        """绘制数码管信息"""
        cv2.putText(self.hud_canvas, "--- Digits ---", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale, self.colors['info'], 1)
        y_offset += self.line_height
        
        # 合并D1/D2为一行显示
        digit_items = []
        overall_status = "OK"
        
        for d_idx in range(NUM_DIGITS):
            if app_state.digit_rois[d_idx] is None:
                continue  # 只显示已定义的数字
            
            rec_char = (app_state.digit_last_recognized_chars[d_idx] 
                       if (d_idx < len(app_state.digit_last_recognized_chars) and 
                           app_state.digit_last_recognized_chars[d_idx] is not None) 
                       else '?')
            
            missing = (app_state.digit_last_missing_segments[d_idx] 
                      if d_idx < len(app_state.digit_last_missing_segments) 
                      else [])
            
            if missing:
                labels = ','.join(DIGIT_SEGMENT_LABELS[s] for s in missing)
                digit_items.append(f"D{d_idx+1}={rec_char} FAIL({labels})")
                overall_status = "FAIL"
            elif rec_char == '?':
                digit_items.append(f"D{d_idx+1}=? Unknown")
                if overall_status != "FAIL":
                    overall_status = "Unknown"
            else:
                digit_items.append(f"D{d_idx+1}={rec_char} OK")
        
        combined_text = ("D: " + " ".join(f"[{it}]" for it in digit_items) 
                        if digit_items else "D: [N/A]")
        
        color = (self.colors['digit_ok'] if overall_status == "OK" 
                else (self.colors['digit_unknown'] if overall_status == "Unknown" 
                      else self.colors['digit_fail']))
        
        cv2.putText(self.hud_canvas, combined_text, (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, color, 1)
        y_offset += self.line_height
        
        # 数码管阈值显示
        cv2.putText(self.hud_canvas, 
                   f"D Th (+/-): {app_state.digit_brightness_threshold:.1f}", 
                   (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, 
                   self.colors['info'], 1)
        y_offset += self.line_height
        
        return y_offset
    
    def _draw_led_info(self, y_offset: int, font_scale: float, 
                      led_font_scale: float, app_state: Any) -> int:
        """绘制LED信息"""
        if app_state.led_max_rois > 0:
            cv2.putText(self.hud_canvas, "--- LEDs ---", (10, y_offset), 
                       cv2.FONT_HERSHEY_SIMPLEX, font_scale, self.colors['info'], 1)
            y_offset += self.line_height
            
            # LED状态
            status_texts_led = []
            for i in range(min(app_state.led_max_rois, len(app_state.led_rois))):
                if not app_state.led_rois[i]:
                    continue  # 只显示已定义的ROI
                
                is_on = (app_state.led_last_status[i] 
                        if i < len(app_state.led_last_status) else False)
                values = (app_state.led_last_values[i] 
                         if i < len(app_state.led_last_values) else (0, 0, 0))
                gray_val, green_val, red_val = values
                is_green_led = (i < app_state.led_num_green)
                led_label = (f"G{i+1}" if is_green_led 
                           else f"R{i - app_state.led_num_green + 1}")
                status_text = "ON" if is_on else "OFF"
                
                status_texts_led.append(
                    f"{led_label}: {status_text} "
                    f"(Gy:{gray_val:.0f},Gn:{green_val:.0f},Rd:{red_val:.0f})"
                )
            
            # 绘制LED状态
            h = self.hud_canvas.shape[0]
            for txt in status_texts_led:
                if y_offset > h - 80:
                    break  # 增加底部边距
                
                is_green_led = txt.startswith("G")
                if ": ON" in txt:
                    color = (self.colors['led_on_green'] if is_green_led 
                           else self.colors['led_on_red'])
                elif ": OFF" in txt:
                    color = self.colors['led_off']
                else:
                    color = self.colors['led_normal']
                
                cv2.putText(self.hud_canvas, txt, (10, y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, color, 1)
                y_offset += self.line_height
            
            # LED阈值显示
            y_offset = self._draw_led_thresholds(y_offset, led_font_scale, app_state)
        
        return y_offset
    
    def _draw_led_thresholds(self, y_offset: int, led_font_scale: float, 
                           app_state: Any) -> int:
        """绘制LED阈值信息"""
        # 绿色LED阈值
        thresh_text_g = (f"G Th: G(g/G)={app_state.led_gray_threshold_green:.1f},"
                        f"Gn(v/V)={app_state.led_green_threshold:.1f}")
        cv2.putText(self.hud_canvas, thresh_text_g, (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, self.colors['info'], 1)
        y_offset += self.line_height
        
        # 红色LED阈值
        thresh_text_r = (f"R Th: G(y/Y)={app_state.led_gray_threshold_red:.1f},"
                        f"Rd(r/R)={app_state.led_red_threshold:.1f}")
        cv2.putText(self.hud_canvas, thresh_text_r, (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, self.colors['info'], 1)
        y_offset += self.line_height
        
        # G33特殊LED阈值
        thresh_text_g33 = (f"G33 Th: G(3/#)={app_state.led_gray_threshold_g33:.1f},"
                          f"Gn(e/E)={app_state.led_green_threshold_g33:.1f}")
        cv2.putText(self.hud_canvas, thresh_text_g33, (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, self.colors['info'], 1)
        y_offset += self.line_height + 3
        
        return y_offset
    
    def _draw_mode_and_fps(self, w: int, font_scale: float, app_state: Any):
        """绘制模式和FPS信息"""
        # 模式信息
        mode_map = {
            MODE_CAMERA_SETTINGS: "Settings", 
            MODE_CALIBRATION: "Calibration", 
            MODE_DETECTION: "Detection"
        }
        mode_str = f"Mode: {mode_map.get(app_state.current_mode, 'Unknown')}"
        
        if app_state.current_mode == MODE_CALIBRATION:
            mode_str += f" (State: {app_state.current_calib_state})"
        
        cv2.putText(self.hud_canvas, mode_str, (w - 280, 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale, self.colors['mode'], 1)
        
        # FPS信息
        fps_text = f"FPS: {app_state.fps:.1f}"
        cv2.putText(self.hud_canvas, fps_text, (w - 280, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale, self.colors['fps'], 1)
        
        # 置顶状态
        fps_text_size = cv2.getTextSize(fps_text, cv2.FONT_HERSHEY_SIMPLEX, 
                                       font_scale, 1)[0]
        topmost_x = w - 280 + fps_text_size[0] + 20
        
        topmost_text = "Topmost: ON" if app_state.window_topmost else "Topmost: OFF"
        topmost_color = (self.colors['topmost_on'] if app_state.window_topmost 
                        else self.colors['topmost_off'])
        cv2.putText(self.hud_canvas, topmost_text, (topmost_x, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale, topmost_color, 1)
    
    def _draw_bottom_info(self, h: int, bottom_font_scale: float, app_state: Any):
        """绘制底部信息"""
        # 异步任务进度信息
        if getattr(app_state, 'async_task_progress', None):
            cv2.putText(self.hud_canvas, app_state.async_task_progress, (10, h - 55), 
                       cv2.FONT_HERSHEY_SIMPLEX, bottom_font_scale, 
                       self.colors['progress'], 1)
        
        # 状态消息
        if getattr(app_state, 'status_message', None):
            cv2.putText(self.hud_canvas, app_state.status_message, (10, h - 35), 
                       cv2.FONT_HERSHEY_SIMPLEX, bottom_font_scale, 
                       self.colors['status'], 1)
        
        # 提示消息
        if getattr(app_state, 'prompt_message', None):
            cv2.putText(self.hud_canvas, app_state.prompt_message, (10, h - 15), 
                       cv2.FONT_HERSHEY_SIMPLEX, bottom_font_scale, 
                       self.colors['prompt'], 1)
    
    def _build_hud_mask(self):
        """构建HUD掩膜"""
        try:
            gray_hud = cv2.cvtColor(self.hud_canvas, cv2.COLOR_BGR2GRAY)
            self.hud_mask = (gray_hud > 0).astype(np.uint8) * 255
        except Exception as e:
            logging.error(f"构建HUD掩膜时出错: {e}")
            self.hud_mask = np.zeros(self.hud_canvas.shape[:2], dtype=np.uint8)
    
    def _overlay_hud(self, display_frame: np.ndarray, hud_canvas: np.ndarray, 
                    hud_mask: np.ndarray):
        """将HUD叠加到显示帧上"""
        try:
            # 确保尺寸匹配
            if (hud_canvas.shape[:2] != display_frame.shape[:2] or 
                hud_mask.shape != display_frame.shape[:2]):
                return
            
            # 使用掩膜叠加HUD
            mask_3ch = cv2.cvtColor(hud_mask, cv2.COLOR_GRAY2BGR)
            mask_norm = mask_3ch.astype(np.float32) / 255.0
            
            # 叠加HUD到显示帧
            display_frame[:] = (display_frame.astype(np.float32) * (1 - mask_norm) + 
                               hud_canvas.astype(np.float32) * mask_norm).astype(np.uint8)
            
        except Exception as e:
            logging.error(f"HUD叠加时出错: {e}")
    
    def clear_hud_cache(self, app_state: Any):
        """清空HUD缓存"""
        self.hud_canvas = None
        self.hud_mask = None
        self.last_hud_update = 0
        
        # 清空app_state中的缓存
        app_state.hud_canvas = None
        app_state.hud_mask = None
        app_state.hud_last_update_ts = 0
        
        logging.debug("HUD cache cleared")
    
    def set_refresh_interval(self, interval: float):
        """设置HUD刷新间隔"""
        self.hud_refresh_interval = max(0.05, interval)  # 最小50ms间隔（20Hz）
        self.set_update_interval(self.hud_refresh_interval)
        logging.debug(f"HUD refresh interval set to {self.hud_refresh_interval:.3f}s")
    
    def cleanup(self):
        """清理资源"""
        super().cleanup()
        self.hud_canvas = None
        self.hud_mask = None
        logging.debug("HUDRenderer cleaned up")
