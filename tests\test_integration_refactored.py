"""
重构后系统的集成测试

测试重构后的完整系统，验证所有模块的协同工作。
包括状态机、UI组件、事件处理器的集成测试。
"""

import pytest
import numpy as np
import cv2
import sys
import os
import time
import threading
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入要测试的模块
import ui_handler_refactored as ui_handler
from app_state import AppState
from constants import *
from state_machine import StateMachineManager
from ui_components import DisplayManager, R<PERSON><PERSON><PERSON><PERSON>, HU<PERSON>enderer
from event_handlers import EventManager, MouseEventHandler, KeyboardEventHandler


@pytest.fixture
def mock_app_state():
    """创建模拟的应用状态"""
    app_state = AppState()
    
    # 设置基本状态
    app_state.current_mode = MODE_CALIBRATION
    app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
    app_state.running = True
    app_state.window_topmost = False
    
    # 设置帧数据
    app_state.current_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    app_state.display_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 设置ROI数据
    app_state.led_rois = [None] * 10
    app_state.led_max_rois = 10
    app_state.calib_led_roi_index = 0
    app_state.led_num_green = 5
    app_state.selected_roi_index = -1
    
    # 设置其他状态
    app_state.selecting_roi = False
    app_state.current_rect = None
    app_state.template_preview_pos = None
    app_state.moving_roi = False
    app_state.template_mode = False
    app_state.fixed_template_mode = False
    app_state.status_message = ""
    app_state.prompt_message = ""
    
    # 设置时间相关
    app_state.prev_time = time.time()
    app_state.fps = 30.0
    app_state.last_task_cleanup_time = time.time()
    
    # 设置HUD相关
    app_state.hud_canvas = None
    app_state.hud_mask = None
    app_state.hud_last_update_ts = 0
    
    # 设置共享状态
    app_state.shared_state = Mock()
    app_state.shared_state.get_and_clear_key.return_value = -1
    
    return app_state


@pytest.fixture
def clean_ui_handler():
    """确保UI处理器在测试前后是干净的"""
    # 测试前清理
    ui_handler.cleanup()
    yield
    # 测试后清理
    ui_handler.cleanup()


class TestUIHandlerRefactored:
    """测试重构后的UI处理器"""
    
    def test_ui_handler_initialization(self, mock_app_state, clean_ui_handler):
        """测试UI处理器初始化"""
        # 设置共享状态应该触发初始化
        ui_handler.set_shared_state(mock_app_state.shared_state)
        
        # 验证管理器已初始化
        stats = ui_handler.get_performance_stats()
        assert stats['initialized'] is True
        assert stats['state_machine_manager'] is not None
        assert stats['display_manager'] is not None
        assert stats['event_manager'] is not None
    
    def test_backward_compatibility_interfaces(self, mock_app_state, clean_ui_handler):
        """测试向后兼容接口"""
        ui_handler.set_shared_state(mock_app_state.shared_state)
        
        # 测试get_key接口
        key = ui_handler.get_key()
        assert isinstance(key, int)
        
        # 测试setup_mouse_callback接口（会失败因为窗口不存在，但不应该抛出异常）
        result = ui_handler.setup_mouse_callback("test_window", mock_app_state)
        assert result is False  # 期望失败，因为窗口不存在
        
        # 测试draw_rois接口
        ui_handler.draw_rois(mock_app_state)  # 应该不抛出异常
        
        # 测试draw_hud接口
        ui_handler.draw_hud(mock_app_state)  # 应该不抛出异常
    
    def test_process_core_logic(self, mock_app_state, clean_ui_handler):
        """测试核心处理逻辑"""
        ui_handler.set_shared_state(mock_app_state.shared_state)
        
        # 模拟必要的依赖
        with patch('async_task_manager.get_task_manager') as mock_task_manager:
            mock_task_manager.return_value.cleanup_old_tasks.return_value = None
            
            result = ui_handler.process_core_logic(mock_app_state)
            assert result is True
    
    def test_prepare_display_frame(self, mock_app_state, clean_ui_handler):
        """测试显示帧准备"""
        ui_handler.set_shared_state(mock_app_state.shared_state)
        
        # 准备显示帧
        frame = ui_handler.prepare_display_frame(mock_app_state)
        
        assert frame is not None
        assert isinstance(frame, np.ndarray)
        assert frame.shape == (480, 640, 3)
    
    def test_error_handling(self, mock_app_state, clean_ui_handler):
        """测试错误处理"""
        ui_handler.set_shared_state(mock_app_state.shared_state)
        
        # 测试无效display_frame的处理
        mock_app_state.display_frame = None
        frame = ui_handler.prepare_display_frame(mock_app_state)
        
        assert frame is not None
        assert isinstance(frame, np.ndarray)
        # 应该返回错误帧
        assert frame.shape == (480, 640, 3)


class TestModuleIntegration:
    """测试模块间集成"""
    
    def test_state_machine_integration(self, mock_app_state):
        """测试状态机集成"""
        # 创建状态机管理器
        sm_manager = StateMachineManager()
        
        # 测试状态处理
        result = sm_manager.process_logic(mock_app_state)
        assert isinstance(result, bool)

        # 测试状态切换
        original_mode = mock_app_state.current_mode
        sm_manager.switch_mode(MODE_DETECTION, mock_app_state)
        # 验证状态已切换或保持原状（取决于具体实现）
        assert mock_app_state.current_mode in [MODE_DETECTION, original_mode]
    
    def test_ui_components_integration(self, mock_app_state):
        """测试UI组件集成"""
        # 创建显示管理器
        display_manager = DisplayManager()
        
        # 添加渲染器
        roi_renderer = ROIRenderer()
        hud_renderer = HUDRenderer()
        
        display_manager.add_renderer(roi_renderer)
        display_manager.add_renderer(hud_renderer)

        # 测试帧渲染
        frame = display_manager.render_all(mock_app_state.display_frame, mock_app_state)
        assert frame is not None
        assert isinstance(frame, np.ndarray)
    
    def test_event_handlers_integration(self, mock_app_state):
        """测试事件处理器集成"""
        # 创建事件管理器
        event_manager = EventManager()
        
        # 添加处理器
        mouse_handler = MouseEventHandler()
        keyboard_handler = KeyboardEventHandler()
        
        event_manager.add_handler(mouse_handler)
        event_manager.add_handler(keyboard_handler)
        
        # 测试鼠标回调设置（会失败因为窗口不存在，但不应该抛出异常）
        result = event_manager.setup_mouse_callback("test_window", mock_app_state)
        assert result is False  # 期望失败，因为窗口不存在
        
        # 测试按键获取
        key = event_manager.get_key()
        assert isinstance(key, int)


class TestFullSystemIntegration:
    """测试完整系统集成"""
    
    @pytest.mark.integration
    def test_complete_system_workflow(self, mock_app_state, clean_ui_handler):
        """测试完整系统工作流程"""
        # 初始化UI处理器
        ui_handler.set_shared_state(mock_app_state.shared_state)
        
        # 模拟完整的处理周期
        with patch('async_task_manager.get_task_manager') as mock_task_manager:
            mock_task_manager.return_value.cleanup_old_tasks.return_value = None
            
            # 1. 处理核心逻辑
            logic_result = ui_handler.process_core_logic(mock_app_state)
            assert logic_result is True
            
            # 2. 准备显示帧
            display_frame = ui_handler.prepare_display_frame(mock_app_state)
            assert display_frame is not None
            
            # 3. 获取性能统计
            stats = ui_handler.get_performance_stats()
            assert stats['initialized'] is True
    
    @pytest.mark.integration
    def test_mode_switching_workflow(self, mock_app_state, clean_ui_handler):
        """测试模式切换工作流程"""
        ui_handler.set_shared_state(mock_app_state.shared_state)
        
        # 测试不同模式下的处理
        modes_to_test = [MODE_CALIBRATION, MODE_DETECTION, MODE_CAMERA_SETTINGS]
        
        for mode in modes_to_test:
            mock_app_state.current_mode = mode
            
            with patch('async_task_manager.get_task_manager') as mock_task_manager:
                mock_task_manager.return_value.cleanup_old_tasks.return_value = None
                
                # 处理核心逻辑
                result = ui_handler.process_core_logic(mock_app_state)
                assert result is True
                
                # 准备显示帧
                frame = ui_handler.prepare_display_frame(mock_app_state)
                assert frame is not None
    
    @pytest.mark.integration
    @pytest.mark.performance
    def test_system_performance(self, mock_app_state, clean_ui_handler):
        """测试系统性能"""
        ui_handler.set_shared_state(mock_app_state.shared_state)
        
        # 性能测试
        iterations = 100
        start_time = time.time()
        
        with patch('async_task_manager.get_task_manager') as mock_task_manager:
            mock_task_manager.return_value.cleanup_old_tasks.return_value = None
            
            for i in range(iterations):
                # 模拟完整的处理周期
                ui_handler.process_core_logic(mock_app_state)
                ui_handler.prepare_display_frame(mock_app_state)
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time_per_iteration = total_time / iterations
        
        # 验证性能要求（每次迭代应该在合理时间内完成）
        assert avg_time_per_iteration < 0.1  # 每次迭代应该少于100ms
        
        # 获取性能统计
        stats = ui_handler.get_performance_stats()
        assert stats['initialized'] is True
    
    @pytest.mark.integration
    def test_error_recovery(self, mock_app_state, clean_ui_handler):
        """测试错误恢复能力"""
        ui_handler.set_shared_state(mock_app_state.shared_state)
        
        # 测试在异常情况下的恢复能力
        
        # 1. 测试无效帧的处理
        mock_app_state.display_frame = None
        frame = ui_handler.prepare_display_frame(mock_app_state)
        assert frame is not None  # 应该返回错误帧而不是崩溃
        
        # 2. 测试无效状态的处理
        mock_app_state.current_mode = 999  # 无效模式
        
        with patch('async_task_manager.get_task_manager') as mock_task_manager:
            mock_task_manager.return_value.cleanup_old_tasks.return_value = None
            
            # 应该能够处理而不崩溃
            result = ui_handler.process_core_logic(mock_app_state)
            # 可能返回False，但不应该抛出异常
            assert isinstance(result, bool)
    
    @pytest.mark.integration
    def test_cleanup_and_reinitialization(self, mock_app_state, clean_ui_handler):
        """测试清理和重新初始化"""
        # 初始化
        ui_handler.set_shared_state(mock_app_state.shared_state)
        stats = ui_handler.get_performance_stats()
        assert stats['initialized'] is True
        
        # 清理
        ui_handler.cleanup()
        stats = ui_handler.get_performance_stats()
        assert stats['initialized'] is False
        
        # 重新初始化
        ui_handler.set_shared_state(mock_app_state.shared_state)
        stats = ui_handler.get_performance_stats()
        assert stats['initialized'] is True
        
        # 验证功能正常
        frame = ui_handler.prepare_display_frame(mock_app_state)
        assert frame is not None


class TestCompatibilityWithOriginal:
    """测试与原始系统的兼容性"""
    
    def test_interface_compatibility(self, mock_app_state, clean_ui_handler):
        """测试接口兼容性"""
        # 测试所有原有的公共接口都存在且可调用
        
        # 设置共享状态
        ui_handler.set_shared_state(mock_app_state.shared_state)
        
        # 测试所有原有接口
        interfaces_to_test = [
            ('get_key', []),
            ('setup_mouse_callback', ['test_window', mock_app_state]),
            ('draw_rois', [mock_app_state]),
            ('draw_hud', [mock_app_state]),
            ('process_core_logic', [mock_app_state]),
            ('prepare_display_frame', [mock_app_state]),
            ('process_ui_and_logic', [mock_app_state]),
        ]
        
        for interface_name, args in interfaces_to_test:
            assert hasattr(ui_handler, interface_name), f"Interface {interface_name} not found"
            
            # 尝试调用接口
            interface_func = getattr(ui_handler, interface_name)
            
            with patch('async_task_manager.get_task_manager') as mock_task_manager:
                mock_task_manager.return_value.cleanup_old_tasks.return_value = None
                
                try:
                    result = interface_func(*args)
                    # 接口应该能够调用而不抛出异常
                    assert True
                except Exception as e:
                    pytest.fail(f"Interface {interface_name} failed: {e}")
    
    def test_global_variables_compatibility(self):
        """测试全局变量兼容性"""
        # 测试原有的全局变量仍然存在
        assert hasattr(ui_handler, 'WIN32_AVAILABLE')
        assert hasattr(ui_handler, 'ANALYSIS_IMPORT_SUCCESS')
        
        # 测试变量类型正确
        assert isinstance(ui_handler.WIN32_AVAILABLE, bool)
        assert isinstance(ui_handler.ANALYSIS_IMPORT_SUCCESS, bool)
