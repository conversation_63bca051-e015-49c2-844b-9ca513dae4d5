"""
重构后的UI处理器 - 门面模式实现

该文件作为门面模式（Facade），保持向后兼容性，同时使用新的模块化架构。
所有原有的公共接口都得到保留，但内部实现使用了重构后的模块。

主要功能：
- 保持原有的公共接口不变
- 将实际实现委托给相应的模块
- 提供统一的错误处理和日志记录
- 管理模块间的协调和通信
"""

import cv2
import numpy as np
import time
import functools
import logging
import subprocess
import os
import sys
import re
import traceback

# Windows API for window topmost functionality
try:
    import win32gui
    import win32con
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    logging.warning("win32gui not available, window topmost feature will use OpenCV fallback")

# 导入原有的依赖模块
from app_state import AppState
from roi_fine_tune import handle_roi_fine_tune, draw_fine_tune_info, init_fine_tune_state
from constants import *
import camera_manager
import config_manager
import led_detector
import digit_detector
from cpu_communicator import send_value_to_cpu
import base_point_manager
import async_task_manager
from async_task_manager import TaskType, TaskStatus

# 导入新的模块化组件
from state_machine import StateMachineManager
from ui_components import DisplayManager, ROIRenderer, HUDRenderer
from event_handlers import EventManager, MouseEventHandler, KeyboardEventHandler, EventPriority

# 尝试导入分析函数，添加错误处理
try:
    from analyze_led_log import analyze_led_cycles
    ANALYSIS_IMPORT_SUCCESS = True
    print("Successfully imported analyze_led_cycles function")
except ImportError as e:
    ANALYSIS_IMPORT_SUCCESS = False
    print(f"Failed to import analyze_led_cycles: {e}")
    analyze_led_cycles = None

# 全局变量和管理器实例
_shared_state = None
_state_machine_manager = None
_display_manager = None
_event_manager = None
_initialized = False


def _initialize_managers():
    """初始化所有管理器（延迟初始化）"""
    global _state_machine_manager, _display_manager, _event_manager, _initialized
    
    if _initialized:
        return
    
    try:
        # 初始化状态机管理器
        _state_machine_manager = StateMachineManager()
        logging.info("StateMachineManager initialized")
        
        # 初始化显示管理器
        _display_manager = DisplayManager()
        
        # 添加渲染器到显示管理器
        roi_renderer = ROIRenderer()
        hud_renderer = HUDRenderer()

        _display_manager.add_renderer(roi_renderer)
        _display_manager.add_renderer(hud_renderer)
        
        logging.info("DisplayManager initialized with ROI and HUD renderers")
        
        # 初始化事件管理器
        _event_manager = EventManager()
        
        # 添加事件处理器到事件管理器
        mouse_handler = MouseEventHandler()
        keyboard_handler = KeyboardEventHandler()
        
        _event_manager.add_handler(mouse_handler, EventPriority.HIGH)
        _event_manager.add_handler(keyboard_handler, EventPriority.NORMAL)
        
        logging.info("EventManager initialized with mouse and keyboard handlers")
        
        _initialized = True
        logging.info("All managers initialized successfully")
        
    except Exception as e:
        logging.error(f"Failed to initialize managers: {e}")
        logging.error(f"Traceback: {traceback.format_exc()}")
        raise


def set_shared_state(shared_state):
    """设置共享状态，用于线程模式下的按键处理"""
    global _shared_state
    _shared_state = shared_state
    
    # 确保管理器已初始化
    _initialize_managers()
    
    # 将共享状态传递给事件管理器
    if _event_manager:
        _event_manager.set_shared_state(shared_state)
        logging.debug("Shared state set for event manager")


def get_key():
    """获取按键，兼容单线程和多线程模式"""
    # 确保管理器已初始化
    _initialize_managers()
    
    if _event_manager:
        return _event_manager.get_key()
    else:
        # 回退到原始实现
        if _shared_state is not None:
            return _shared_state.get_and_clear_key()
        else:
            return cv2.waitKey(1) & 0xFF


def toggle_window_topmost(window_title, topmost):
    """切换窗口置顶状态，优先使用 Windows API，回退到 OpenCV"""
    success = False
    
    if WIN32_AVAILABLE:
        try:
            # 查找窗口句柄
            hwnd = win32gui.FindWindow(None, window_title)
            if hwnd:
                if topmost:
                    # 设置为置顶
                    win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST, 0, 0, 0, 0,
                                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
                else:
                    # 取消置顶
                    win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, 0, 0, 0, 0,
                                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
                success = True
                logging.debug(f"Window '{window_title}' topmost set to {topmost} using Win32 API")
            else:
                logging.warning(f"Window '{window_title}' not found for topmost operation")
        except Exception as e:
            logging.error(f"Win32 API topmost operation failed: {e}")
    
    if not success:
        # 回退到 OpenCV 方法（功能有限）
        try:
            if topmost:
                cv2.setWindowProperty(window_title, cv2.WND_PROP_TOPMOST, 1)
            else:
                cv2.setWindowProperty(window_title, cv2.WND_PROP_TOPMOST, 0)
            success = True
            logging.debug(f"Window '{window_title}' topmost set to {topmost} using OpenCV fallback")
        except Exception as e:
            logging.error(f"OpenCV topmost operation failed: {e}")
    
    return success


def setup_mouse_callback(window_name: str, app_state: AppState):
    """设置鼠标回调"""
    # 确保管理器已初始化
    _initialize_managers()
    
    if _event_manager:
        return _event_manager.setup_mouse_callback(window_name, app_state)
    else:
        # 回退到原始实现
        logging.warning("EventManager not available, using fallback mouse callback")
        callback_with_state = functools.partial(_mouse_callback_fallback, app_state=app_state)
        cv2.setMouseCallback(window_name, callback_with_state)
        return True


def _mouse_callback_fallback(event, x, y, flags, param, app_state: AppState):
    """回退的鼠标回调实现（如果事件管理器不可用）"""
    logging.warning("Using fallback mouse callback - event handling may be limited")
    # 这里可以实现基本的鼠标处理逻辑，或者直接忽略


def draw_rois(app_state: AppState):
    """绘制所有ROI（向后兼容接口）"""
    # 确保管理器已初始化
    _initialize_managers()
    
    if _display_manager and app_state.display_frame is not None:
        try:
            # 使用ROI渲染器绘制
            roi_renderer = _display_manager.get_renderer("roi")
            if roi_renderer:
                roi_renderer.render(app_state.display_frame, app_state)
            else:
                logging.warning("ROI renderer not found in display manager")
        except Exception as e:
            logging.error(f"Failed to draw ROIs using new renderer: {e}")
            # 可以在这里添加回退逻辑


def draw_hud(app_state: AppState):
    """绘制HUD信息（向后兼容接口）"""
    # 确保管理器已初始化
    _initialize_managers()
    
    if _display_manager and app_state.display_frame is not None:
        try:
            # 使用HUD渲染器绘制
            hud_renderer = _display_manager.get_renderer("hud")
            if hud_renderer:
                hud_renderer.render(app_state.display_frame, app_state)
            else:
                logging.warning("HUD renderer not found in display manager")
        except Exception as e:
            logging.error(f"Failed to draw HUD using new renderer: {e}")
            # 可以在这里添加回退逻辑


def process_core_logic(app_state: AppState):
    """核心处理逻辑（向后兼容接口）"""
    # 确保管理器已初始化
    _initialize_managers()
    
    try:
        # --- 更新处理 FPS ---
        curr_time = time.time()
        time_diff = curr_time - app_state.prev_time
        if time_diff > 0:
            app_state.fps = 1.0 / time_diff
        app_state.prev_time = curr_time

        # --- 定期清理异步任务 ---
        if curr_time - app_state.last_task_cleanup_time > 60.0:  # 每60秒清理一次
            task_manager = async_task_manager.get_task_manager()
            task_manager.cleanup_old_tasks()
            app_state.last_task_cleanup_time = curr_time

        # --- 使用状态机管理器处理核心逻辑 ---
        if _state_machine_manager:
            return _state_machine_manager.process_logic(app_state)
        else:
            logging.error("StateMachineManager not available")
            return False
            
    except Exception as e:
        logging.error(f"Error in process_core_logic: {e}")
        logging.error(f"Traceback: {traceback.format_exc()}")
        return False


def prepare_display_frame(app_state: AppState):
    """准备显示帧（向后兼容接口）"""
    # 确保管理器已初始化
    _initialize_managers()
    
    try:
        if app_state.display_frame is not None and isinstance(app_state.display_frame, np.ndarray):
            # 使用显示管理器渲染所有组件
            if _display_manager:
                return _display_manager.render_all(app_state.display_frame, app_state)
            else:
                logging.warning("DisplayManager not available, using fallback")
                # 回退到基本的绘制
                draw_rois(app_state)
                draw_hud(app_state)
                draw_fine_tune_info(app_state, app_state.display_frame)
                return app_state.display_frame.copy()
        else:
            # 创建错误信息帧
            logging.warning(f"display_frame is invalid: {type(app_state.display_frame)}")
            error_frame = np.zeros((480, 640, 3), dtype=np.uint8)
            cv2.putText(error_frame, "Error: display_frame is invalid", (50, 240),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            return error_frame
            
    except Exception as e:
        logging.error(f"Error in prepare_display_frame: {e}")
        # 返回错误帧
        error_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.putText(error_frame, f"Error: {str(e)[:50]}", (50, 240),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        return error_frame


def process_ui_and_logic(app_state: AppState):
    """兼容性函数，保持原有接口（单线程模式）"""
    try:
        process_core_logic(app_state)
        
        # 准备并显示帧
        display_frame = prepare_display_frame(app_state)
        cv2.imshow(MAIN_WINDOW, display_frame)
        
    except Exception as e:
        logging.error(f"Error in process_ui_and_logic: {e}")


def get_performance_stats():
    """获取性能统计信息（新增接口）"""
    stats = {
        'initialized': _initialized,
        'state_machine_manager': None,
        'display_manager': None,
        'event_manager': None
    }
    
    try:
        if _state_machine_manager:
            stats['state_machine_manager'] = _state_machine_manager.get_status_info()
        
        if _display_manager:
            stats['display_manager'] = _display_manager.get_performance_stats()
        
        if _event_manager:
            stats['event_manager'] = _event_manager.get_performance_stats()
            
    except Exception as e:
        logging.error(f"Error getting performance stats: {e}")
        stats['error'] = str(e)
    
    return stats


def cleanup():
    """清理所有管理器资源（新增接口）"""
    global _state_machine_manager, _display_manager, _event_manager, _initialized
    
    try:
        if _state_machine_manager:
            # StateMachineManager没有cleanup方法，直接设为None
            _state_machine_manager = None
        
        if _display_manager:
            _display_manager.cleanup()
            _display_manager = None
        
        if _event_manager:
            _event_manager.cleanup()
            _event_manager = None
        
        _initialized = False
        logging.info("All managers cleaned up successfully")
        
    except Exception as e:
        logging.error(f"Error during cleanup: {e}")


# 保持原有的全局变量和常量
WIN32_AVAILABLE = WIN32_AVAILABLE
ANALYSIS_IMPORT_SUCCESS = ANALYSIS_IMPORT_SUCCESS
