"""
鼠标事件处理器

负责处理所有鼠标相关的事件，包括：
- 基准点选择
- ROI拖拽选择
- ROI编辑和移动
- 模板模式处理
"""

import cv2
import numpy as np
from typing import Any, Dict, Tuple, Optional
import logging

from .base_event_handler import BaseEventHandler, EventType
from constants import *


class MouseEventHandler(BaseEventHandler):
    """鼠标事件处理器"""
    
    def __init__(self):
        """初始化鼠标事件处理器"""
        super().__init__("MouseEventHandler")
        
        # 添加支持的事件类型
        self.add_supported_event(EventType.MOUSE_CLICK)
        self.add_supported_event(EventType.MOUSE_MOVE)
        self.add_supported_event(EventType.MOUSE_RELEASE)
        
        # 鼠标状态跟踪
        self.is_dragging = False
        self.drag_start_pos = None
        self.last_mouse_pos = (0, 0)
        
        # ROI选择状态
        self.selecting_roi = False
        self.roi_start_point = None
        
        # ROI移动状态
        self.moving_roi = False
        self.move_start_pos = None
        self.original_roi_pos = None
        self.selected_roi_index = -1
        
        logging.debug("MouseEventHandler initialized")
    
    def handle_event(self, event_type: EventType, event_data: Dict[str, Any], 
                    app_state: Any) -> bool:
        """
        处理鼠标事件
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
            app_state: 应用状态
            
        Returns:
            是否成功处理事件
        """
        if not self.validate_event_data(event_type, event_data):
            return False
        
        x = event_data.get('x', 0)
        y = event_data.get('y', 0)
        cv_event = event_data.get('cv_event', 0)
        flags = event_data.get('flags', 0)
        
        # 更新鼠标位置
        self.last_mouse_pos = (x, y)
        
        # 检查是否允许处理鼠标事件
        if not self._is_mouse_interaction_allowed(app_state):
            self._reset_mouse_state(app_state)
            return False
        
        # 根据校准状态分发事件
        if app_state.current_calib_state == CALIB_STATE_BASE_POINTS_SELECT:
            return self._handle_base_point_selection(cv_event, x, y, app_state)
        elif app_state.current_calib_state == CALIB_STATE_LED_ROI_SELECT:
            return self._handle_led_roi_selection(cv_event, x, y, flags, app_state)
        elif app_state.current_calib_state == CALIB_STATE_LED_EDIT:
            return self._handle_led_roi_editing(cv_event, x, y, flags, app_state)
        elif app_state.current_calib_state in [CALIB_STATE_DIGIT_ROI_SELECT_1, 
                                              CALIB_STATE_DIGIT_ROI_SELECT_2]:
            return self._handle_digit_roi_selection(cv_event, x, y, app_state)
        elif app_state.current_calib_state == CALIB_STATE_DIGIT_SEGMENT_SELECT:
            return self._handle_digit_segment_selection(cv_event, x, y, app_state)
        
        return False
    
    def _is_mouse_interaction_allowed(self, app_state: Any) -> bool:
        """检查是否允许鼠标交互"""
        return (app_state.current_mode == MODE_CALIBRATION and
                app_state.current_calib_state in [
                    CALIB_STATE_BASE_POINTS_SELECT,
                    CALIB_STATE_LED_ROI_SELECT,
                    CALIB_STATE_LED_EDIT,
                    CALIB_STATE_DIGIT_ROI_SELECT_1,
                    CALIB_STATE_DIGIT_ROI_SELECT_2,
                    CALIB_STATE_DIGIT_SEGMENT_SELECT
                ])
    
    def _reset_mouse_state(self, app_state: Any):
        """重置鼠标状态"""
        self.selecting_roi = False
        self.moving_roi = False
        self.is_dragging = False
        app_state.selecting_roi = False
        app_state.current_rect = None
        app_state.template_preview_pos = None
    
    def _handle_base_point_selection(self, cv_event: int, x: int, y: int, 
                                   app_state: Any) -> bool:
        """处理基准点选择"""
        if cv_event == cv2.EVENT_LBUTTONDOWN:
            idx = app_state.calib_base_point_index
            if idx < 2:
                try:
                    # 导入base_point_manager
                    import base_point_manager
                    
                    # 提取基准点模板
                    template = base_point_manager.extract_base_template(
                        app_state.current_frame, x, y, app_state.base_template_size
                    )
                    if template is not None:
                        app_state.base_points[idx] = (x, y)
                        app_state.base_templates[idx] = template
                        print(f"✓ 基准点 {idx + 1} 已选择: ({x}, {y}), 模板大小: {template.shape}")
                        app_state.calib_base_point_index += 1

                        # 给出下一步提示
                        if idx == 0:
                            print("请选择第二个基准点（建议选择与第一个点有一定距离的特征点）")
                        else:
                            print("两个基准点已选择完成，按Enter继续或按R重新选择")
                    else:
                        print("❌ 基准点选择失败！请选择对比度更高的特征点（如螺丝孔、标记、清晰边角）")
                        print("提示：避免选择平坦区域或光照不均匀的位置")

                except Exception as e:
                    print(f"❌ 基准点选择时发生错误: {e}")
                    logging.error(f"基准点选择异常: {e}")
            return True
        return False
    
    def _handle_led_roi_selection(self, cv_event: int, x: int, y: int, flags: int, 
                                app_state: Any) -> bool:
        """处理LED ROI选择"""
        # 模板模式处理
        if ((app_state.template_mode and app_state.template_roi) or 
            app_state.fixed_template_mode):
            return self._handle_template_mode(cv_event, x, y, app_state)
        
        # 常规拖拽模式处理
        return self._handle_regular_roi_selection(cv_event, x, y, app_state)
    
    def _handle_template_mode(self, cv_event: int, x: int, y: int, app_state: Any) -> bool:
        """处理模板模式"""
        if cv_event == cv2.EVENT_MOUSEMOVE:
            # 更新模板预览位置 (以鼠标为中心)
            if app_state.template_mode and app_state.template_roi:
                w, h = app_state.template_roi
            elif app_state.fixed_template_mode:
                w = h = app_state.fixed_template_size
            else:
                return False
            app_state.template_preview_pos = (x - w//2, y - h//2, w, h)
            return True

        elif cv_event == cv2.EVENT_LBUTTONDOWN:
            # 检查是否超出 LED ROI 数量限制
            if app_state.calib_led_roi_index >= app_state.led_max_rois:
                print(f"已达到最大 LED ROI 数量 ({app_state.led_max_rois})。")
                return True
            
            # 在点击位置放置模板ROI
            if app_state.template_mode and app_state.template_roi:
                w, h = app_state.template_roi
                print(f"模板ROI已放置在位置: ({x - w//2}, {y - h//2})")
            elif app_state.fixed_template_mode:
                w = h = app_state.fixed_template_size
                print(f"固定尺寸ROI({w}x{h})已放置在位置: ({x - w//2}, {y - h//2})")
            else:
                return False
            
            app_state.current_rect = (x - w//2, y - h//2, w, h)
            return True
        
        return False
    
    def _handle_regular_roi_selection(self, cv_event: int, x: int, y: int, 
                                    app_state: Any) -> bool:
        """处理常规ROI选择（拖拽模式）"""
        if cv_event == cv2.EVENT_LBUTTONDOWN:
            # 检查是否超出 LED ROI 数量限制
            if app_state.calib_led_roi_index >= app_state.led_max_rois:
                print(f"已达到最大 LED ROI 数量 ({app_state.led_max_rois})。")
                return True
            
            self.selecting_roi = True
            app_state.selecting_roi = True
            self.roi_start_point = (x, y)
            app_state.roi_start_point = (x, y)
            app_state.current_rect = None
            return True

        elif cv_event == cv2.EVENT_MOUSEMOVE:
            if self.selecting_roi and self.roi_start_point:
                x1, y1 = self.roi_start_point
                x2, y2 = x, y
                start_x, start_y = min(x1, x2), min(y1, y2)
                end_x, end_y = max(x1, x2), max(y1, y2)
                w, h = end_x - start_x, end_y - start_y
                
                # 只有当 ROI 有效时才更新 current_rect
                if w > 0 and h > 0:
                    app_state.current_rect = (start_x, start_y, w, h)
                else:
                    app_state.current_rect = None
                return True

        elif cv_event == cv2.EVENT_LBUTTONUP:
            if self.selecting_roi:
                self.selecting_roi = False
                app_state.selecting_roi = False
                
                # 检查 current_rect 是否有效
                if (app_state.current_rect is None or 
                    app_state.current_rect[2] < ROI_MIN_SIZE or 
                    app_state.current_rect[3] < ROI_MIN_SIZE):
                    print("ROI 选择无效 (尺寸过小或未移动)。")
                    app_state.current_rect = None
                return True
        
        return False
    
    def _handle_led_roi_editing(self, cv_event: int, x: int, y: int, flags: int, 
                              app_state: Any) -> bool:
        """处理LED ROI编辑"""
        if cv_event == cv2.EVENT_LBUTTONDOWN:
            # 检查是否点击在某个ROI上
            clicked_roi_index = -1
            for i, roi in enumerate(app_state.led_rois):
                if roi and isinstance(roi, tuple) and len(roi) == 4:
                    rx, ry, rw, rh = roi
                    if rx <= x <= rx + rw and ry <= y <= ry + rh:
                        clicked_roi_index = i
                        break

            if clicked_roi_index != -1:
                # 点击在ROI上，开始移动
                self.selected_roi_index = clicked_roi_index
                app_state.selected_roi_index = clicked_roi_index
                self.moving_roi = True
                app_state.moving_roi = True
                self.move_start_pos = (x, y)
                app_state.move_start_pos = (x, y)
                self.original_roi_pos = app_state.led_rois[clicked_roi_index]
                app_state.original_roi_pos = app_state.led_rois[clicked_roi_index]
                
                # 同步到 current_rect，便于 WASD 微调与步长显示
                app_state.current_rect = app_state.led_rois[clicked_roi_index]
                
                is_green = clicked_roi_index < app_state.led_num_green
                led_label = (f"G{clicked_roi_index+1}" if is_green 
                           else f"R{clicked_roi_index - app_state.led_num_green + 1}")
                print(f"开始移动 {led_label} ROI")
            else:
                # 点击在空白处，取消选择
                self.selected_roi_index = -1
                app_state.selected_roi_index = -1
                self.moving_roi = False
                app_state.moving_roi = False
                app_state.current_rect = None
            return True

        elif cv_event == cv2.EVENT_MOUSEMOVE:
            if (self.moving_roi and self.selected_roi_index != -1 and 
                self.move_start_pos and self.original_roi_pos):
                # 计算移动偏移量
                start_x, start_y = self.move_start_pos
                offset_x = x - start_x
                offset_y = y - start_y

                # 更新ROI位置
                orig_x, orig_y, orig_w, orig_h = self.original_roi_pos
                new_x = orig_x + offset_x
                new_y = orig_y + offset_y
                new_roi = (new_x, new_y, orig_w, orig_h)
                app_state.led_rois[self.selected_roi_index] = new_roi
                
                # 同步 current_rect，便于 WASD 微调实时作用
                app_state.current_rect = new_roi
            return True

        elif cv_event == cv2.EVENT_LBUTTONUP:
            if self.moving_roi:
                self.moving_roi = False
                app_state.moving_roi = False
                
                if self.selected_roi_index != -1:
                    is_green = self.selected_roi_index < app_state.led_num_green
                    led_label = (f"G{self.selected_roi_index+1}" if is_green 
                               else f"R{self.selected_roi_index - app_state.led_num_green + 1}")
                    new_pos = app_state.led_rois[self.selected_roi_index]
                    print(f"{led_label} ROI 移动完成: {new_pos}")
            return True
        
        return False
    
    def _handle_digit_roi_selection(self, cv_event: int, x: int, y: int, 
                                  app_state: Any) -> bool:
        """处理数码管ROI选择"""
        return self._handle_regular_roi_selection(cv_event, x, y, app_state)
    
    def _handle_digit_segment_selection(self, cv_event: int, x: int, y: int, 
                                      app_state: Any) -> bool:
        """处理数码管段选择"""
        return self._handle_regular_roi_selection(cv_event, x, y, app_state)
    
    def validate_event_data(self, event_type: EventType, event_data: Dict[str, Any]) -> bool:
        """验证鼠标事件数据"""
        if not super().validate_event_data(event_type, event_data):
            return False
        
        # 检查必需的鼠标事件字段
        required_fields = ['x', 'y', 'cv_event']
        for field in required_fields:
            if field not in event_data:
                logging.error(f"Missing required field '{field}' in mouse event data")
                return False
        
        # 验证坐标范围
        x, y = event_data['x'], event_data['y']
        if x < 0 or y < 0 or x > 10000 or y > 10000:  # 合理的坐标范围
            logging.warning(f"Mouse coordinates out of range: ({x}, {y})")
            return False
        
        return True
    
    def get_mouse_state(self) -> Dict[str, Any]:
        """获取当前鼠标状态"""
        return {
            'last_pos': self.last_mouse_pos,
            'is_dragging': self.is_dragging,
            'selecting_roi': self.selecting_roi,
            'moving_roi': self.moving_roi,
            'selected_roi_index': self.selected_roi_index,
            'roi_start_point': self.roi_start_point,
            'move_start_pos': self.move_start_pos
        }
    
    def reset_mouse_state(self):
        """重置鼠标状态"""
        self.is_dragging = False
        self.selecting_roi = False
        self.moving_roi = False
        self.selected_roi_index = -1
        self.roi_start_point = None
        self.move_start_pos = None
        self.original_roi_pos = None
        logging.debug("Mouse state reset")
    
    def cleanup(self):
        """清理资源"""
        self.reset_mouse_state()
        super().cleanup()
        logging.debug("MouseEventHandler cleaned up")
