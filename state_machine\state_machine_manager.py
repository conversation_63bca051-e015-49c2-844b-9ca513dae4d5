"""
状态机管理器

负责管理多个状态机实例，协调不同模式间的切换，
提供统一的状态机访问接口。
"""

from typing import Dict, Optional, Any, List
import logging
import time

from .base_state_machine import BaseStateMachine
from constants import MODE_CAMERA_SETTINGS, MODE_CALIBRATION, MODE_DETECTION


class StateMachineManager:
    """状态机管理器"""

    def __init__(self):
        """初始化状态机管理器"""
        self.state_machines: Dict[str, BaseStateMachine] = {}
        self.current_mode = MODE_CAMERA_SETTINGS
        self.mode_history: List[tuple] = [(MODE_CAMERA_SETTINGS, time.time())]
        self.last_process_time = 0
        self.process_interval = 0.001  # 1ms间隔，避免过度占用CPU

        # 自动注册所有状态机
        self._register_all_state_machines()

        logging.info("StateMachineManager initialized")

    def _register_all_state_machines(self):
        """自动注册所有状态机"""
        from .camera_state_machine import CameraStateMachine
        from .calibration_state_machine import CalibrationStateMachine
        from .detection_state_machine import DetectionStateMachine

        # 注册各种模式的状态机
        self.register_state_machine(MODE_CAMERA_SETTINGS, CameraStateMachine())
        self.register_state_machine(MODE_CALIBRATION, CalibrationStateMachine())
        self.register_state_machine(MODE_DETECTION, DetectionStateMachine())
    
    def register_state_machine(self, mode: str, state_machine: BaseStateMachine):
        """注册状态机"""
        self.state_machines[mode] = state_machine
        logging.info(f"Registered state machine for mode: {mode}")
    
    def unregister_state_machine(self, mode: str):
        """注销状态机"""
        if mode in self.state_machines:
            del self.state_machines[mode]
            logging.info(f"Unregistered state machine for mode: {mode}")
    
    def get_state_machine(self, mode: str = None) -> Optional[BaseStateMachine]:
        """获取状态机实例"""
        if mode is None:
            mode = self.current_mode
        return self.state_machines.get(mode)
    
    def get_current_state_machine(self) -> Optional[BaseStateMachine]:
        """获取当前模式的状态机"""
        return self.get_state_machine(self.current_mode)
    
    def switch_mode(self, new_mode: str, app_state: Any = None) -> bool:
        """切换模式"""
        if new_mode not in self.state_machines:
            logging.error(f"Unknown mode: {new_mode}")
            return False
        
        if new_mode == self.current_mode:
            logging.debug(f"Already in mode: {new_mode}")
            return True
        
        old_mode = self.current_mode
        
        # 通知旧状态机模式切换
        old_state_machine = self.get_state_machine(old_mode)
        if old_state_machine:
            self._on_mode_exit(old_state_machine, app_state)
        
        # 切换模式
        self.current_mode = new_mode
        self.mode_history.append((new_mode, time.time()))
        
        # 更新app_state中的模式
        if app_state:
            app_state.current_mode = new_mode
        
        # 通知新状态机模式切换
        new_state_machine = self.get_state_machine(new_mode)
        if new_state_machine:
            self._on_mode_enter(new_state_machine, app_state)
        
        logging.info(f"Mode switched: {old_mode} -> {new_mode}")
        return True
    
    def process_logic(self, app_state: Any) -> bool:
        """处理当前模式的逻辑"""
        current_time = time.time()
        
        # 控制处理频率，避免过度占用CPU
        if current_time - self.last_process_time < self.process_interval:
            return True
        
        self.last_process_time = current_time
        
        # 获取当前状态机
        state_machine = self.get_current_state_machine()
        if state_machine is None:
            logging.error(f"No state machine for current mode: {self.current_mode}")
            return False
        
        # 处理状态机逻辑
        try:
            return state_machine.process_state(app_state)
        except Exception as e:
            logging.error(f"State machine processing failed: {e}")
            return False
    
    def can_switch_to_mode(self, mode: str, app_state: Any = None) -> bool:
        """检查是否可以切换到指定模式"""
        if mode not in self.state_machines:
            return False
        
        # 检查当前状态机是否允许切换
        current_state_machine = self.get_current_state_machine()
        if current_state_machine:
            return self._can_exit_mode(current_state_machine, app_state)
        
        return True
    
    def get_current_mode(self) -> str:
        """获取当前模式"""
        return self.current_mode
    
    def get_current_state(self) -> Optional[str]:
        """获取当前状态机的当前状态"""
        state_machine = self.get_current_state_machine()
        if state_machine:
            return state_machine.get_current_state()
        return None
    
    def get_available_modes(self) -> List[str]:
        """获取可用的模式列表"""
        return list(self.state_machines.keys())
    
    def get_mode_history(self) -> List[tuple]:
        """获取模式历史"""
        return self.mode_history.copy()
    
    def reset_current_state_machine(self):
        """重置当前状态机到初始状态"""
        state_machine = self.get_current_state_machine()
        if state_machine:
            state_machine.reset_to_initial()
            logging.info(f"Reset state machine for mode: {self.current_mode}")
    
    def get_status_info(self) -> Dict[str, Any]:
        """获取状态机管理器的状态信息"""
        current_state_machine = self.get_current_state_machine()
        
        info = {
            'current_mode': self.current_mode,
            'available_modes': self.get_available_modes(),
            'mode_count': len(self.state_machines),
            'current_state': None,
            'state_duration': 0,
            'available_transitions': []
        }
        
        if current_state_machine:
            info.update({
                'current_state': current_state_machine.get_current_state(),
                'state_duration': current_state_machine.get_state_duration(),
                'available_transitions': current_state_machine.get_available_transitions()
            })
        
        return info
    
    def _on_mode_enter(self, state_machine: BaseStateMachine, app_state: Any):
        """模式进入时的处理"""
        # 调用状态机的模式进入处理
        if hasattr(state_machine, 'on_mode_enter'):
            state_machine.on_mode_enter(app_state)

    def _on_mode_exit(self, state_machine: BaseStateMachine, app_state: Any):
        """模式退出时的处理"""
        # 调用状态机的模式退出处理
        if hasattr(state_machine, 'on_mode_exit'):
            state_machine.on_mode_exit(app_state)

    def _can_exit_mode(self, state_machine: BaseStateMachine, app_state: Any) -> bool:
        """检查是否可以退出当前模式"""
        # 检查状态机是否允许退出
        if hasattr(state_machine, 'can_exit_mode'):
            return state_machine.can_exit_mode(app_state)
        return True
    
    def shutdown(self):
        """关闭状态机管理器"""
        logging.info("Shutting down StateMachineManager")
        
        # 清理所有状态机
        for mode, state_machine in self.state_machines.items():
            try:
                # 如果状态机有清理方法，调用它
                if hasattr(state_machine, 'cleanup'):
                    state_machine.cleanup()
            except Exception as e:
                logging.error(f"Error cleaning up state machine for {mode}: {e}")
        
        self.state_machines.clear()
        logging.info("StateMachineManager shutdown complete")
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"StateMachineManager(current_mode='{self.current_mode}', modes={len(self.state_machines)})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"StateMachineManager(current_mode='{self.current_mode}', "
                f"available_modes={list(self.state_machines.keys())}, "
                f"current_state='{self.get_current_state()}')")
