"""
pytest配置文件和共享fixtures
"""
import pytest
import sys
import os
import numpy as np
import cv2
from unittest.mock import Mock, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_state import AppState
from constants import *


@pytest.fixture
def mock_app_state():
    """创建模拟的AppState实例"""
    app_state = AppState()
    
    # 设置基本属性
    app_state.running = True
    app_state.current_mode = MODE_DETECTION
    app_state.current_calib_state = CALIB_STATE_START
    
    # 设置显示帧
    app_state.display_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 设置ROI
    app_state.led_rois = [None] * 10
    app_state.digit_rois = [None, None]
    app_state.base_points = [None] * 4
    
    # 设置阈值
    app_state.led_gray_threshold_green = 100.0
    app_state.led_green_threshold = 50.0
    app_state.led_gray_threshold_red = 120.0
    app_state.led_red_threshold = 60.0
    
    # 设置状态
    app_state.led_last_status = [False] * 10
    app_state.led_last_values = [0.0] * 10
    
    return app_state


@pytest.fixture
def mock_camera():
    """创建模拟的摄像头对象"""
    mock_cap = Mock()
    mock_cap.isOpened.return_value = True
    mock_cap.read.return_value = (True, np.zeros((480, 640, 3), dtype=np.uint8))
    mock_cap.set.return_value = True
    mock_cap.get.return_value = 30.0
    return mock_cap


@pytest.fixture
def sample_frame():
    """创建示例图像帧"""
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    # 添加一些测试图案
    cv2.rectangle(frame, (100, 100), (200, 200), (0, 255, 0), -1)  # 绿色矩形
    cv2.rectangle(frame, (300, 300), (400, 400), (0, 0, 255), -1)  # 红色矩形
    return frame


@pytest.fixture
def mock_shared_state():
    """创建模拟的共享状态对象"""
    shared_state = Mock()
    shared_state.should_exit = False
    shared_state.get_and_clear_key.return_value = -1
    shared_state.set_last_key = Mock()
    shared_state.request_exit = Mock()
    return shared_state


@pytest.fixture
def mock_led_detector():
    """创建模拟的LED检测器"""
    with pytest.MonkeyPatch().context() as m:
        mock_detect = Mock()
        mock_detect.return_value = None  # LED检测直接修改app_state
        m.setattr("led_detector.detect_led_status", mock_detect)
        yield mock_detect


@pytest.fixture
def mock_digit_detector():
    """创建模拟的数码管检测器"""
    with pytest.MonkeyPatch().context() as m:
        mock_detect = Mock()
        mock_detect.return_value = (['8', '8'], [[1,1,1,1,1,1,1], [1,1,1,1,1,1,1]], 0.9)
        m.setattr("digit_detector.detect_digit_status", mock_detect)
        yield mock_detect


@pytest.fixture
def mock_config_manager():
    """创建模拟的配置管理器"""
    with pytest.MonkeyPatch().context() as m:
        mock_save = Mock(return_value=True)
        mock_load = Mock()
        m.setattr("config_manager.save_config", mock_save)
        m.setattr("config_manager.load_config", mock_load)
        yield {"save": mock_save, "load": mock_load}


@pytest.fixture
def mock_cv2():
    """创建模拟的OpenCV函数"""
    with pytest.MonkeyPatch().context() as m:
        mock_imshow = Mock()
        mock_waitkey = Mock(return_value=-1)
        mock_setmousecallback = Mock()
        mock_namedwindow = Mock()
        mock_resizewindow = Mock()
        
        m.setattr("cv2.imshow", mock_imshow)
        m.setattr("cv2.waitKey", mock_waitkey)
        m.setattr("cv2.setMouseCallback", mock_setmousecallback)
        m.setattr("cv2.namedWindow", mock_namedwindow)
        m.setattr("cv2.resizeWindow", mock_resizewindow)
        
        yield {
            "imshow": mock_imshow,
            "waitKey": mock_waitkey,
            "setMouseCallback": mock_setmousecallback,
            "namedWindow": mock_namedwindow,
            "resizeWindow": mock_resizewindow
        }


class TestHelper:
    """测试辅助类"""
    
    @staticmethod
    def create_roi(x, y, w, h):
        """创建ROI元组"""
        return (x, y, w, h)
    
    @staticmethod
    def create_test_frame(width=640, height=480, channels=3):
        """创建测试用的图像帧"""
        return np.zeros((height, width, channels), dtype=np.uint8)
    
    @staticmethod
    def assert_roi_valid(roi, min_size=10):
        """断言ROI有效性"""
        assert roi is not None, "ROI should not be None"
        assert len(roi) == 4, "ROI should have 4 elements (x, y, w, h)"
        x, y, w, h = roi
        assert w >= min_size, f"ROI width {w} should be >= {min_size}"
        assert h >= min_size, f"ROI height {h} should be >= {min_size}"
        assert x >= 0 and y >= 0, "ROI coordinates should be non-negative"
    
    @staticmethod
    def assert_frame_valid(frame, expected_shape=None):
        """断言图像帧有效性"""
        assert frame is not None, "Frame should not be None"
        assert isinstance(frame, np.ndarray), "Frame should be numpy array"
        if expected_shape:
            assert frame.shape == expected_shape, f"Frame shape {frame.shape} != expected {expected_shape}"


@pytest.fixture
def test_helper():
    """提供测试辅助类实例"""
    return TestHelper


# 性能测试相关的fixtures
@pytest.fixture
def performance_threshold():
    """性能测试阈值"""
    return {
        'max_processing_time': 0.1,  # 最大处理时间100ms
        'max_memory_increase': 0.1,  # 最大内存增长10%
        'min_fps': 25.0,  # 最小帧率25FPS
    }


# 集成测试相关的fixtures
@pytest.fixture
def integration_test_config():
    """集成测试配置"""
    return {
        'test_duration': 5.0,  # 测试持续时间5秒
        'frame_count': 150,    # 测试帧数
        'roi_count': 5,        # 测试ROI数量
    }


# 标记定义
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "performance: 性能测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "ui: UI相关测试"
    )
    config.addinivalue_line(
        "markers", "core: 核心算法测试"
    )


# 测试数据目录
@pytest.fixture
def test_data_dir():
    """测试数据目录"""
    return os.path.join(os.path.dirname(__file__), "test_data")


# 临时测试目录
@pytest.fixture
def temp_test_dir(tmp_path):
    """临时测试目录"""
    return tmp_path
