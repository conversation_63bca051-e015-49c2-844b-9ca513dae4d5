[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    slow: 慢速测试（运行时间较长）
    ui: UI相关测试
    core: 核心算法测试
    regression: 回归测试

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# 覆盖率配置（如果安装了pytest-cov）
# addopts = 
#     -v
#     --tb=short
#     --strict-markers
#     --disable-warnings
#     --color=yes
#     --durations=10
#     --cov=.
#     --cov-report=html
#     --cov-report=term-missing
#     --cov-fail-under=80

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 最小版本要求
minversion = 6.0

# 测试超时（如果安装了pytest-timeout）
# timeout = 300

# 并行测试（如果安装了pytest-xdist）
# addopts = -n auto
