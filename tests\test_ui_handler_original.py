"""
测试原始ui_handler.py的功能
用于重构前后的对比验证
"""
import pytest
import numpy as np
import cv2
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import ui_handler
from app_state import AppState
from constants import *


class TestUIHandlerOriginal:
    """测试原始ui_handler.py的功能"""
    
    def test_set_shared_state(self, mock_shared_state):
        """测试设置共享状态"""
        ui_handler.set_shared_state(mock_shared_state)
        assert ui_handler._shared_state == mock_shared_state
    
    def test_get_key_with_shared_state(self, mock_shared_state):
        """测试在多线程模式下获取按键"""
        mock_shared_state.get_and_clear_key.return_value = ord('q')
        ui_handler.set_shared_state(mock_shared_state)
        
        key = ui_handler.get_key()
        assert key == ord('q')
        mock_shared_state.get_and_clear_key.assert_called_once()
    
    @patch('cv2.waitKey')
    def test_get_key_without_shared_state(self, mock_waitkey):
        """测试在单线程模式下获取按键"""
        mock_waitkey.return_value = ord('c')
        ui_handler.set_shared_state(None)
        
        key = ui_handler.get_key()
        assert key == ord('c')
        mock_waitkey.assert_called_once_with(1)
    
    @patch('cv2.setMouseCallback')
    def test_setup_mouse_callback(self, mock_setmousecallback, mock_app_state):
        """测试设置鼠标回调"""
        window_name = "test_window"
        ui_handler.setup_mouse_callback(window_name, mock_app_state)
        
        mock_setmousecallback.assert_called_once()
        args = mock_setmousecallback.call_args[0]
        assert args[0] == window_name
        assert callable(args[1])  # 回调函数应该是可调用的
    
    def test_draw_rois_with_none_frame(self, mock_app_state):
        """测试当display_frame为None时的ROI绘制"""
        mock_app_state.display_frame = None
        
        # 应该不会抛出异常
        ui_handler.draw_rois(mock_app_state)
    
    def test_draw_rois_with_valid_frame(self, mock_app_state, sample_frame):
        """测试有效帧的ROI绘制"""
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.led_rois[0] = (100, 100, 50, 50)
        mock_app_state.led_rois[1] = (200, 200, 50, 50)
        
        # 应该不会抛出异常
        ui_handler.draw_rois(mock_app_state)
        
        # 验证帧没有变成None
        assert mock_app_state.display_frame is not None
    
    def test_draw_hud_with_none_frame(self, mock_app_state):
        """测试当display_frame为None时的HUD绘制"""
        mock_app_state.display_frame = None
        
        # 应该不会抛出异常
        ui_handler.draw_hud(mock_app_state)
    
    def test_draw_hud_with_valid_frame(self, mock_app_state, sample_frame):
        """测试有效帧的HUD绘制"""
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.fps = 30.0
        mock_app_state.led_last_status = [True, False, True] + [False] * 7
        
        # 应该不会抛出异常
        ui_handler.draw_hud(mock_app_state)
        
        # 验证帧没有变成None
        assert mock_app_state.display_frame is not None
    
    def test_prepare_display_frame_with_valid_frame(self, mock_app_state, sample_frame):
        """测试准备显示帧（有效帧）"""
        mock_app_state.display_frame = sample_frame.copy()
        
        result_frame = ui_handler.prepare_display_frame(mock_app_state)
        
        assert result_frame is not None
        assert isinstance(result_frame, np.ndarray)
        assert result_frame.shape == sample_frame.shape
    
    def test_prepare_display_frame_with_none_frame(self, mock_app_state):
        """测试准备显示帧（无效帧）"""
        mock_app_state.display_frame = None
        
        result_frame = ui_handler.prepare_display_frame(mock_app_state)
        
        assert result_frame is not None
        assert isinstance(result_frame, np.ndarray)
        assert result_frame.shape == (480, 640, 3)  # 错误帧的默认尺寸
    
    @patch('ui_handler._run_detection_mode')
    def test_process_core_logic_detection_mode(self, mock_run_detection, mock_app_state):
        """测试检测模式的核心逻辑处理"""
        mock_app_state.current_mode = MODE_DETECTION
        
        result = ui_handler.process_core_logic(mock_app_state)
        
        assert result is True
        mock_run_detection.assert_called_once_with(mock_app_state)
    
    @patch('ui_handler._run_calibration_mode')
    def test_process_core_logic_calibration_mode(self, mock_run_calibration, mock_app_state):
        """测试校准模式的核心逻辑处理"""
        mock_app_state.current_mode = MODE_CALIBRATION
        
        result = ui_handler.process_core_logic(mock_app_state)
        
        assert result is True
        mock_run_calibration.assert_called_once_with(mock_app_state)
    
    @patch('ui_handler._run_camera_settings_mode')
    def test_process_core_logic_camera_mode(self, mock_run_camera, mock_app_state):
        """测试相机设置模式的核心逻辑处理"""
        mock_app_state.current_mode = MODE_CAMERA_SETTINGS
        
        result = ui_handler.process_core_logic(mock_app_state)
        
        assert result is True
        mock_run_camera.assert_called_once_with(mock_app_state)
    
    def test_process_core_logic_unknown_mode(self, mock_app_state):
        """测试未知模式的处理"""
        mock_app_state.current_mode = "UNKNOWN_MODE"
        
        result = ui_handler.process_core_logic(mock_app_state)
        
        assert result is True
        assert mock_app_state.running is False  # 应该设置为停止运行
    
    @patch('cv2.imshow')
    @patch('ui_handler.prepare_display_frame')
    @patch('ui_handler.process_core_logic')
    def test_process_ui_and_logic(self, mock_core_logic, mock_prepare_frame, 
                                  mock_imshow, mock_app_state, sample_frame):
        """测试UI和逻辑处理（单线程模式）"""
        mock_prepare_frame.return_value = sample_frame
        mock_core_logic.return_value = True
        
        ui_handler.process_ui_and_logic(mock_app_state)
        
        mock_core_logic.assert_called_once_with(mock_app_state)
        mock_prepare_frame.assert_called_once_with(mock_app_state)
        mock_imshow.assert_called_once_with(MAIN_WINDOW, sample_frame)


class TestUIHandlerModeHandlers:
    """测试ui_handler中的模式处理函数"""
    
    @patch('ui_handler.get_key')
    def test_run_camera_settings_mode_no_camera(self, mock_get_key, mock_app_state):
        """测试相机设置模式（无摄像头）"""
        mock_app_state.cap = None
        mock_get_key.return_value = -1
        
        ui_handler._run_camera_settings_mode(mock_app_state)
        
        assert mock_app_state.running is False
    
    @patch('ui_handler.get_key')
    def test_run_camera_settings_mode_with_camera(self, mock_get_key, mock_app_state, 
                                                   mock_camera, sample_frame):
        """测试相机设置模式（有摄像头）"""
        mock_app_state.cap = mock_camera
        mock_camera.read.return_value = (True, sample_frame)
        mock_get_key.return_value = -1
        
        ui_handler._run_camera_settings_mode(mock_app_state)
        
        assert mock_app_state.display_frame is not None
        mock_camera.read.assert_called_once()
    
    @patch('ui_handler.get_key')
    def test_run_detection_mode_no_camera(self, mock_get_key, mock_app_state):
        """测试检测模式（无摄像头）"""
        mock_app_state.cap = None
        mock_get_key.return_value = -1
        
        ui_handler._run_detection_mode(mock_app_state)
        
        assert mock_app_state.running is False


class TestUIHandlerPerformance:
    """测试ui_handler的性能"""
    
    @pytest.mark.performance
    def test_prepare_display_frame_performance(self, mock_app_state, sample_frame, 
                                               performance_threshold):
        """测试准备显示帧的性能"""
        import time
        
        mock_app_state.display_frame = sample_frame
        
        start_time = time.time()
        for _ in range(100):  # 运行100次
            ui_handler.prepare_display_frame(mock_app_state)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 100
        assert avg_time < performance_threshold['max_processing_time']
    
    @pytest.mark.performance
    def test_draw_rois_performance(self, mock_app_state, sample_frame, 
                                   performance_threshold):
        """测试ROI绘制的性能"""
        import time
        
        mock_app_state.display_frame = sample_frame
        mock_app_state.current_mode = MODE_DETECTION
        # 设置多个ROI
        for i in range(10):
            mock_app_state.led_rois[i] = (i*50, i*40, 40, 30)
        
        start_time = time.time()
        for _ in range(100):  # 运行100次
            ui_handler.draw_rois(mock_app_state)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 100
        assert avg_time < performance_threshold['max_processing_time']


class TestUIHandlerIntegration:
    """ui_handler的集成测试"""
    
    @pytest.mark.integration
    @patch('led_detector.detect_led_status')
    @patch('digit_detector.detect_digit_status')
    def test_detection_mode_integration(self, mock_digit_detect, mock_led_detect,
                                        mock_app_state, mock_camera, sample_frame):
        """测试检测模式的集成功能"""
        # 设置模拟
        mock_app_state.cap = mock_camera
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.led_analysis_state = 'IDLE'
        mock_camera.read.return_value = (True, sample_frame)
        mock_digit_detect.return_value = (['8', '8'], [[1,1,1,1,1,1,1], [1,1,1,1,1,1,1]], 0.9)
        
        # 运行检测模式
        with patch('ui_handler.get_key', return_value=-1):
            ui_handler._run_detection_mode(mock_app_state)
        
        # 验证调用
        mock_led_detect.assert_called()
        mock_digit_detect.assert_called()
        assert mock_app_state.display_frame is not None
