"""存储项目所需的所有常量"""

# --- 程序模式 ---
MODE_CAMERA_SETTINGS = 0
MODE_CALIBRATION = 1
MODE_DETECTION = 2

# --- 校准子状态 (在 MODE_CALIBRATION 中使用) ---
CALIB_STATE_START = 10
CALIB_STATE_BASE_POINTS_SELECT = 9  # 基准点选择状态
CALIB_STATE_LED_ROI_SELECT = 11
CALIB_STATE_LED_SAMPLE_OFF = 12
CALIB_STATE_LED_SAMPLE_ON = 13
CALIB_STATE_LED_ANALYZE = 14
CALIB_STATE_LED_EDIT = 15  # LED ROI 编辑模式
CALIB_STATE_DIGIT_CAPTURE_88 = 20
CALIB_STATE_DIGIT_ROI_SELECT_1 = 21
CALIB_STATE_DIGIT_ROI_SELECT_2 = 22
CALIB_STATE_DIGIT_SEGMENT_SELECT = 23
CALIB_STATE_DIGIT_CAPTURE_BG = 24
CALIB_STATE_DIGIT_ADJUST_THRESHOLD = 25
CALIB_STATE_DONE = 30 # 虽然校准完成直接返回 START, 但保留定义可能有用

# --- 数码管段标签 ---
DIGIT_SEGMENT_LABELS = ['a', 'b', 'c', 'd', 'e', 'f', 'g']

# --- 数码管字符映射 ---
SEGMENT_MAP = {
    (1, 1, 1, 1, 1, 1, 0): '0', (0, 1, 1, 0, 0, 0, 0): '1',
    (1, 1, 0, 1, 1, 0, 1): '2', (1, 1, 1, 1, 0, 0, 1): '3',
    (0, 1, 1, 0, 0, 1, 1): '4', (1, 0, 1, 1, 0, 1, 1): '5',
    (1, 0, 1, 1, 1, 1, 1): '6', (1, 1, 1, 0, 0, 0, 0): '7',
    (1, 1, 1, 1, 1, 1, 1): '8', (1, 1, 1, 1, 0, 1, 1): '9',
    # 大写字母 A-F
    (1, 1, 1, 0, 1, 1, 1): 'A', (1, 0, 0, 1, 1, 1, 1): 'B', # 'B' 和 'E' 的定义相同，这里用E的定义
    (1, 0, 0, 1, 1, 1, 0): 'C', (0, 1, 1, 1, 1, 0, 1): 'D', # 'D' 和 'd' 的定义相同，这里用d的定义
    (1, 0, 0, 1, 1, 1, 1): 'E',
    (1, 0, 0, 0, 1, 1, 1): 'F',
    # 特殊字符
    (0, 1, 0, 0, 0, 1, 0): 'I', # 添加'I'字符映射(两侧竖线点亮)
    # 小写字母 a-f (注意 b, c, d, e, f 与大写或数字可能冲突或未明确定义，需根据实际硬件调整)
    (1, 1, 1, 1, 1, 0, 1): 'a', # 和 0 类似但 g 不同?
    (0, 0, 1, 1, 1, 1, 1): 'b',
    (0, 0, 0, 1, 1, 0, 1): 'c',
    (0, 1, 1, 1, 1, 0, 1): 'd',
    (1, 0, 0, 1, 1, 1, 1): 'e', # 和 E 一样
    (1, 0, 0, 0, 1, 1, 1): 'f', # 和 F 一样
    (1, 1, 0, 0, 1, 1, 1): 'P',
    (0, 0, 0, 1, 1, 1, 0): 'L',
    (0, 0, 0, 0, 0, 0, 0): '-', # 全灭状态
}
# 创建反向映射，注意处理重复值（后面识别出的模式优先映射到哪个字符）
# 这里简单地用第一个遇到的值为准，如果硬件显示不同，可能需要调整 SEGMENT_MAP
CHAR_TO_SEGMENTS = {v: k for k, v in SEGMENT_MAP.items()}

# --- 文件与窗口名称 ---
CONFIG_FILE = "combined_config.json"
MAIN_WINDOW = "LED & Digit Detector - Press 'h' for help v1.5"
SETTINGS_WINDOW = "Camera Settings" # 可能不再需要单独窗口
CALIBRATION_WINDOW = "Calibration Mode" # 可能不再需要单独窗口
DETECTION_WINDOW = "Detection Mode" # 可能不再需要单独窗口

# --- 摄像头参数 ---
DEFAULT_CAMERA_INDEX = 0
FALLBACK_CAMERA_INDEX = 1 # Fallback to 1 instead of 0
RESOLUTION_PRESETS = [
    (640, 480), (800, 600), (1280, 720), (1920, 1080), (2560, 1440)
]
DEFAULT_RESOLUTION_INDEX = 2 # Default 1280x720
DEFAULT_EXPOSURE = -5.0
DEFAULT_BRIGHTNESS = 0.0
EXPOSURE_STEP = 1.0
BRIGHTNESS_STEP = 10.0

# --- LED 检测参数 ---
DEFAULT_NUM_GREEN_LEDS = 33
DEFAULT_NUM_RED_LEDS = 2
DEFAULT_LED_GRAY_THRESHOLD_GREEN = 160.0
DEFAULT_LED_GREEN_THRESHOLD = 180.0
DEFAULT_LED_GRAY_THRESHOLD_RED = 160.0
DEFAULT_LED_RED_THRESHOLD = 100.0
LED_THRESHOLD_STEP = 5.0
LED_SAMPLE_FRAMES = 10 # 采集样本的帧数

# --- 数码管检测参数 ---
NUM_DIGITS = 2
NUM_SEGMENTS_PER_DIGIT = 7
DEFAULT_DIGIT_BRIGHTNESS_THRESHOLD = 50.0
DIGIT_THRESHOLD_STEP = 1.0

# --- UI & 校准辅助 ---
ROI_MIN_SIZE = 5 # ROI 拖拽确认的最小尺寸

# --- 其他常量 ---
FPS_UPDATE_INTERVAL = 1.0 # FPS 更新频率（秒）
MAX_SIDEBAR_TEXT_LINES = 25 # 侧边栏最大文本行数 (示例，如果需要)
LED_HISTORY_SIZE = 5 # LED 状态判断的时间平滑窗口大小

# --- 基准点对齐参数 ---
BASE_TEMPLATE_SIZE = 30  # 基准点模板大小

# --- 固定尺寸模板参数 ---
DEFAULT_FIXED_TEMPLATE_SIZE = 12  # 默认固定模板大小（像素）
BASE_MATCH_THRESHOLD = 0.75  # 默认匹配阈值
BASE_ALIGNMENT_TIMEOUT = 5  # 连续失败超时次数

# --- Logging Constants ---
LOG_FILE = 'led_digit_detection.log'
DEFAULT_LOGGING_DURATION = 50.0  # 默认日志记录时间（秒）

# === 第一阶段精度优化参数 ===
ENABLE_PRECISION_OPT_STAGE1 = True        # 总开关
BRIGHT_PIXEL_PERCENT = 0.10               # 采样最亮10%像素
BRIGHTNESS_K_ENABLE = False               # 亮度系数k（暂不启用）

# 性能监控
PERFORMANCE_WARNING_THRESHOLD_MS = 2.0    # 性能告警阈值（毫秒）

# === G33特殊LED配置 ===
SPECIAL_LED_G33_INDEX = 32                # G33在LED数组中的索引（从0开始，第33个绿色LED）
SPECIAL_LED_INDICES = [32]                # 特殊LED索引列表，便于扩展
DEFAULT_LED_GRAY_THRESHOLD_G33 = 130.0    # G33独立灰度阈值
DEFAULT_LED_GREEN_THRESHOLD_G33 = 180.0   # G33独立绿色通道阈值
