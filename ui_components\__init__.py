"""
UI组件模块

该模块负责管理所有UI元素的绘制和显示，包括：
- ROI绘制（LED、数码管、基准点等）
- HUD显示（状态信息、提示信息等）
- 显示管理（帧准备、渲染器组合）
- 窗口管理（置顶、大小等）

主要组件：
- BaseRenderer: 渲染器基类
- ROIRenderer: ROI绘制渲染器
- HUDRenderer: HUD显示渲染器
- DisplayManager: 显示管理器
- WindowManager: 窗口管理器
"""

from .base_renderer import BaseRenderer
from .display_manager import DisplayManager
from .roi_renderer import ROIRenderer
from .hud_renderer import HUDRenderer

# 在模块完成后导入具体的渲染器类
# from .window_manager import WindowManager

__all__ = [
    'BaseRenderer',
    'DisplayManager',
    'ROIRenderer',
    'HUDRenderer',
    # 'WindowManager',
]

__version__ = '1.0.0'
__author__ = 'UI Handler Refactor Team'
