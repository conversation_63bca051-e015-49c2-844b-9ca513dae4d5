# 项目结构与架构说明（最新）

本项目已采用“主线程 GUI + 处理线程（ProcessingWorker）”的稳定解耦架构：
- 主线程仅负责窗口显示与键盘输入（cv2.imshow + cv2.waitKey），避免 OpenCV 在子线程渲染导致灰窗
- 处理线程 ProcessingWorker 负责采集/检测/准备显示帧，并以非阻塞方式将“最新帧”放入队列（maxsize=1，丢旧留新）
- Windows 下全局启用 1ms 计时器（timeBeginPeriod），确保最小化/可见状态下处理频率一致性

关于 display_thread.py 与 processing_thread.py：
- processing_thread.py：正在使用（负责采集/检测/准备显示帧）
- display_thread.py：当前未启用（main.py 中 USE_DISPLAY_THREAD = False），作为可选方案保留

## 目录结构（AA05取消红色灯珠重构01/）

AA05取消红色灯珠重构01/
├── README.md                         # 本说明文档
├── main.py                           # 程序入口；初始化、启动采集/处理线程；主线程负责 GUI
├── constants.py                      # 常量定义（模式码、状态码、阈值、窗口名等）
├── app_state.py                      # 共享状态：含 display_queue、frame_queue、FPS、ROI、阈值等
├── config_manager.py                 # 配置加载/保存（combined_config.json）
├── camera_manager.py                 # 摄像头初始化与参数应用（DSHOW/MSMF 等）
├── ui_handler.py                     # 核心业务：模式状态机、绘制 HUD/ROI、process_core_logic/prepare_display_frame
├── roi_fine_tune.py                  # ROI 微调（WASD/Shift+WASD/数字步长）
├── led_detector.py                   # LED 检测逻辑（若存在）
├── digit_detector.py                 # 数码管检测/识别逻辑（若存在）
├── capture_thread.py                 # 采集线程（cap.read → frame_queue）
├── processing_thread.py              # 处理线程（检测→ display_queue）
├── display_thread.py                 # 可选显示线程（当前未启用；历史兼容保留）
├── high_res_timer.py                 # Windows 全局 1ms 计时器（timeBeginPeriod/timeEndPeriod）
├── async_task_manager.py             # 异步任务管理（分析任务/CPU通信等）
├── simple_websocket_server.py        # 简易 WebSocket 服务器（启动/停止）
├── combined_config.json              # 运行后生成/维护的组合配置（如已存在）
├── led_digit_detection.log           # 检测日志（按状态机在LOGGING窗口写入）
├── doc/
│   ├── 优化方案.md
│   └── 问题分析/
│       └── UI界面缩放导致日志稀疏.md # 原因与解决方案说明
├── testdoc/                          # 测试脚本与样例
├── model_temp/                       # 中间模型/实验脚本目录（如存在）
├── requirements.txt
└── requirements_websocket.txt

## 运行关键点
- main.py 中 USE_DISPLAY_THREAD = False，确保 GUI 在主线程渲染
- 程序启动即启用 1ms 计时器（high_res_timer.enable_1ms），退出自动恢复
- 处理线程持续产出最新帧；主线程从队列取“最新一帧”并显示

## 变更摘要
- 新增 processing_thread.py，负责采集/检测，与 GUI 完全解耦
- 保留 display_thread.py 作为可选方案；默认关闭
- 新增 high_res_timer.py，解决“窗口最小化导致日志稀疏”的计时器分辨率问题
- 文档新增：doc/问题分析/UI界面缩放导致日志稀疏.md
