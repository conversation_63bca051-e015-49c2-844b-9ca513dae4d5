"""
相机设置模式状态机

负责处理相机设置模式的状态转换和逻辑处理，包括：
- 相机参数调整（分辨率、曝光、亮度）
- 设置保存
- 模式切换到校准模式
"""

import cv2
import numpy as np
import time
import logging

from .base_state_machine import BaseStateMachine, StateTransition
from constants import *
import camera_manager
import config_manager


class CameraStateMachine(BaseStateMachine):
    """相机设置模式状态机"""
    
    # 相机设置模式的状态
    STATE_CAMERA_INIT = "CAMERA_INIT"
    STATE_CAMERA_SETTINGS = "CAMERA_SETTINGS"
    STATE_CAMERA_ERROR = "CAMERA_ERROR"
    STATE_SETTINGS_SAVED = "SETTINGS_SAVED"
    
    def __init__(self):
        """初始化相机设置状态机"""
        super().__init__(self.STATE_CAMERA_INIT, "CameraStateMachine")
        
        # 状态持续时间跟踪
        self.error_display_duration = 1.0  # 错误显示持续时间
        self.save_feedback_duration = 2.0  # 保存反馈显示时间
    
    def setup_transitions(self):
        """设置状态转换规则"""
        # 初始化 -> 设置（相机可用）
        self.add_transition(StateTransition(
            self.STATE_CAMERA_INIT,
            self.STATE_CAMERA_SETTINGS,
            condition=self._camera_available
        ))
        
        # 初始化 -> 错误（相机不可用）
        self.add_transition(StateTransition(
            self.STATE_CAMERA_INIT,
            self.STATE_CAMERA_ERROR,
            condition=lambda ctx: not self._camera_available(ctx)
        ))
        
        # 设置 -> 错误（相机读取失败）
        self.add_transition(StateTransition(
            self.STATE_CAMERA_SETTINGS,
            self.STATE_CAMERA_ERROR,
            condition=self._camera_read_failed
        ))
        
        # 设置 -> 已保存（保存设置）
        self.add_transition(StateTransition(
            self.STATE_CAMERA_SETTINGS,
            self.STATE_SETTINGS_SAVED,
            condition=self._settings_save_requested
        ))
        
        # 已保存 -> 设置（继续调整）
        self.add_transition(StateTransition(
            self.STATE_SETTINGS_SAVED,
            self.STATE_CAMERA_SETTINGS,
            condition=lambda ctx: self.get_state_duration() > self.save_feedback_duration
        ))
        
        # 错误状态自动恢复到初始化（显示错误一段时间后）
        self.add_transition(StateTransition(
            self.STATE_CAMERA_ERROR,
            self.STATE_CAMERA_INIT,
            condition=lambda ctx: self.get_state_duration() > self.error_display_duration
        ))
    
    def setup_handlers(self):
        """设置状态处理器"""
        self.add_state_handler(self.STATE_CAMERA_INIT, self._handle_camera_init)
        self.add_state_handler(self.STATE_CAMERA_SETTINGS, self._handle_camera_settings)
        self.add_state_handler(self.STATE_CAMERA_ERROR, self._handle_camera_error)
        self.add_state_handler(self.STATE_SETTINGS_SAVED, self._handle_settings_saved)
    
    def _camera_available(self, app_state) -> bool:
        """检查相机是否可用"""
        return (app_state.cap is not None and 
                app_state.cap.isOpened())
    
    def _camera_read_failed(self, app_state) -> bool:
        """检查相机读取是否失败"""
        if not self._camera_available(app_state):
            return True
        
        try:
            ret, frame = app_state.cap.read()
            return not ret or frame is None
        except Exception:
            return True
    
    def _settings_save_requested(self, app_state) -> bool:
        """检查是否请求保存设置"""
        # 这个条件在按键处理中设置
        return getattr(app_state, '_camera_save_requested', False)
    
    def _handle_camera_init(self, app_state) -> bool:
        """处理相机初始化状态"""
        # 检查相机状态并尝试转换
        if self._camera_available(app_state):
            self.transition_to(self.STATE_CAMERA_SETTINGS, app_state)
        else:
            self.transition_to(self.STATE_CAMERA_ERROR, app_state)
        
        return True
    
    def _handle_camera_settings(self, app_state) -> bool:
        """处理相机设置状态"""
        # 检查相机读取
        if self._camera_read_failed(app_state):
            self.transition_to(self.STATE_CAMERA_ERROR, app_state)
            return True
        
        # 读取并显示当前帧
        ret, frame = app_state.cap.read()
        if ret and frame is not None:
            app_state.display_frame = frame.copy()
        
        # 显示当前设置信息
        self._update_settings_display(app_state)
        
        # 处理按键输入
        self._handle_camera_input(app_state)
        
        # 检查是否请求保存
        if self._settings_save_requested(app_state):
            self.transition_to(self.STATE_SETTINGS_SAVED, app_state)
        
        return True
    
    def _handle_camera_error(self, app_state) -> bool:
        """处理相机错误状态"""
        app_state.prompt_message = "Error: Camera not available!"
        app_state.status_message = "Check camera connection and restart"
        
        # 创建错误显示帧
        h, w = (480, 640)  # 默认尺寸
        if app_state.display_frame is not None:
            h, w = app_state.display_frame.shape[:2]
        
        app_state.display_frame = np.zeros((h, w, 3), dtype=np.uint8)
        
        # 在错误显示时间后自动重试
        if self.get_state_duration() > self.error_display_duration:
            self.transition_to(self.STATE_CAMERA_INIT, app_state)
        
        return True
    
    def _handle_settings_saved(self, app_state) -> bool:
        """处理设置已保存状态"""
        app_state.status_message = "Camera settings saved!"
        app_state.prompt_message = "Settings saved successfully"
        
        # 清除保存请求标志
        app_state._camera_save_requested = False
        
        # 显示保存反馈一段时间后返回设置状态
        if self.get_state_duration() > self.save_feedback_duration:
            self.transition_to(self.STATE_CAMERA_SETTINGS, app_state)
        
        return True
    
    def _update_settings_display(self, app_state):
        """更新设置显示信息"""
        res_w, res_h = RESOLUTION_PRESETS[app_state.current_resolution_index]
        settings_text = [
            f"Resolution: {res_w}x{res_h} ('T'/'t')",
            f"Exposure: {app_state.exposure_value:.1f} ('E'/'e')",
            f"Brightness: {app_state.brightness_value:.1f} ('B'/'b')"
        ]
        app_state.status_message = " | ".join(settings_text)
        app_state.prompt_message = "Press 'S' Save | 'Enter' Calibrate | 'Q' Quit"
    
    def _handle_camera_input(self, app_state):
        """处理相机设置的按键输入"""
        # 导入get_key函数
        from ui_handler import get_key
        
        key = get_key()
        needs_reapply = False
        
        if key == ord('q'):
            app_state.running = False
            
        elif key == ord('t'):  # 降低分辨率
            app_state.current_resolution_index = (
                app_state.current_resolution_index - 1 + len(RESOLUTION_PRESETS)
            ) % len(RESOLUTION_PRESETS)
            needs_reapply = True
            
        elif key == ord('T'):  # 增加分辨率
            app_state.current_resolution_index = (
                app_state.current_resolution_index + 1
            ) % len(RESOLUTION_PRESETS)
            needs_reapply = True
            
        elif key == ord('e'):  # 减少曝光
            app_state.exposure_value -= EXPOSURE_STEP
            if app_state.cap:
                app_state.cap.set(cv2.CAP_PROP_EXPOSURE, float(app_state.exposure_value))
                
        elif key == ord('E'):  # 增加曝光
            app_state.exposure_value += EXPOSURE_STEP
            if app_state.cap:
                app_state.cap.set(cv2.CAP_PROP_EXPOSURE, float(app_state.exposure_value))
                
        elif key == ord('b'):  # 减少亮度
            app_state.brightness_value -= BRIGHTNESS_STEP
            if app_state.cap:
                app_state.cap.set(cv2.CAP_PROP_BRIGHTNESS, float(app_state.brightness_value))
                
        elif key == ord('B'):  # 增加亮度
            app_state.brightness_value += BRIGHTNESS_STEP
            if app_state.cap:
                app_state.cap.set(cv2.CAP_PROP_BRIGHTNESS, float(app_state.brightness_value))
                
        elif key == ord('s'):  # 保存设置
            logging.info("Saving camera settings...")
            if config_manager.save_config(app_state):
                app_state._camera_save_requested = True
            else:
                logging.error("Failed to save camera settings")
                
        elif key == 13:  # Enter - 切换到校准模式
            logging.info("Camera settings confirmed, switching to Calibration mode")
            # 保存当前配置
            config_manager.save_config(app_state)
            # 请求模式切换（由StateMachineManager处理）
            app_state._mode_switch_requested = MODE_CALIBRATION
            app_state.current_calib_state = CALIB_STATE_START
            app_state.current_rect = None
        
        # 如果分辨率改变，重新应用所有设置
        if needs_reapply:
            logging.info("Resolution changed, reapplying camera settings...")
            if not camera_manager.apply_camera_settings(app_state.cap, app_state):
                logging.error("Failed to apply new camera settings")
                app_state.prompt_message = "Error: Failed to apply camera settings!"
                # 转换到错误状态
                self.transition_to(self.STATE_CAMERA_ERROR, app_state)
    
    def can_exit_mode(self, app_state) -> bool:
        """检查是否可以退出相机设置模式"""
        # 相机设置模式通常可以随时退出
        return True
    
    def on_mode_enter(self, app_state):
        """进入相机设置模式时的处理"""
        logging.info("Entered Camera Settings mode")
        # 重置到初始状态
        self.reset_to_initial(self.STATE_CAMERA_INIT)
        # 清除任何保存请求标志
        app_state._camera_save_requested = False
    
    def on_mode_exit(self, app_state):
        """退出相机设置模式时的处理"""
        logging.info("Exited Camera Settings mode")
        # 清理状态
        app_state._camera_save_requested = False
