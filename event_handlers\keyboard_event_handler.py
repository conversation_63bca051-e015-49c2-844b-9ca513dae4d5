"""
键盘事件处理器

负责处理所有键盘相关的事件，包括：
- 模式切换按键
- ROI微调按键（WASD）
- 功能按键（保存、重置等）
- 数字键选择
- 特殊功能键
"""

import cv2
import time
from typing import Any, Dict, List, Optional, Callable
import logging

from .base_event_handler import BaseEventHandler, EventType
from constants import *


class KeyboardEventHandler(BaseEventHandler):
    """键盘事件处理器"""
    
    def __init__(self):
        """初始化键盘事件处理器"""
        super().__init__("KeyboardEventHandler")
        
        # 添加支持的事件类型
        self.add_supported_event(EventType.KEY_PRESS)
        
        # 共享状态管理（用于多线程模式）
        self._shared_state = None
        
        # 按键映射和处理器
        self.key_handlers: Dict[int, Callable] = {}
        self.mode_key_handlers: Dict[int, Dict[int, Callable]] = {}
        
        # 按键状态跟踪
        self.last_key = -1
        self.last_key_time = 0
        self.key_repeat_delay = 0.1  # 按键重复延迟（秒）
        
        # ROI微调配置
        self.roi_fine_tune_step = 1  # 微调步长
        self.roi_fine_tune_large_step = 10  # 大步长微调
        
        # 初始化按键处理器
        self._setup_key_handlers()
        
        logging.debug("KeyboardEventHandler initialized")
    
    def set_shared_state(self, shared_state: Any):
        """设置共享状态（用于多线程模式）"""
        self._shared_state = shared_state
        logging.debug(f"Shared state set: {shared_state is not None}")
    
    def get_key(self) -> int:
        """获取按键，兼容单线程和多线程模式"""
        if self._shared_state is not None:
            # 多线程模式：从共享状态获取按键
            return self._shared_state.get_and_clear_key()
        else:
            # 单线程模式：直接调用 waitKey
            return cv2.waitKey(1) & 0xFF
    
    def handle_event(self, event_type: EventType, event_data: Dict[str, Any], 
                    app_state: Any) -> bool:
        """
        处理键盘事件
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
            app_state: 应用状态
            
        Returns:
            是否成功处理事件
        """
        if event_type != EventType.KEY_PRESS:
            return False
        
        key = event_data.get('key', -1)
        if key == -1 or key == 255:
            return False
        
        # 检查按键重复
        current_time = time.time()
        if (key == self.last_key and 
            (current_time - self.last_key_time) < self.key_repeat_delay):
            return False
        
        self.last_key = key
        self.last_key_time = current_time
        
        # 处理按键
        return self._process_key(key, app_state)
    
    def _setup_key_handlers(self):
        """设置按键处理器"""
        # 全局按键处理器
        self.key_handlers = {
            ord('q'): self._handle_quit,
            ord('w'): self._handle_window_topmost,
            ord('c'): self._handle_calibration_mode,
            ord('s'): self._handle_save,
            ord('h'): self._handle_help,
            27: self._handle_escape,  # ESC键
        }
        
        # 模式特定按键处理器
        self.mode_key_handlers = {
            MODE_CAMERA_SETTINGS: {
                ord('t'): self._handle_resolution_down,
                ord('T'): self._handle_resolution_up,
                ord('e'): self._handle_exposure_down,
                ord('E'): self._handle_exposure_up,
                ord('b'): self._handle_brightness_down,
                ord('B'): self._handle_brightness_up,
                ord('g'): self._handle_gain_down,
                ord('G'): self._handle_gain_up,
                ord('r'): self._handle_reset_camera,
                13: self._handle_camera_confirm,  # Enter键
            },
            MODE_CALIBRATION: {
                ord('b'): self._handle_base_point_calibration,
                ord('l'): self._handle_led_calibration,
                ord('d'): self._handle_digit_calibration,
                ord('r'): self._handle_reset_calibration,
                ord('n'): self._handle_next_roi,
                ord('p'): self._handle_previous_roi,
                ord('t'): self._handle_template_mode,
                ord('f'): self._handle_fixed_template_mode,
                ord('m'): self._handle_manual_mode,
                13: self._handle_calibration_confirm,  # Enter键
                # ROI微调按键
                ord('w'): self._handle_roi_up,
                ord('a'): self._handle_roi_left,
                ord('s'): self._handle_roi_down,
                ord('d'): self._handle_roi_right,
                ord('W'): self._handle_roi_up_large,
                ord('A'): self._handle_roi_left_large,
                ord('S'): self._handle_roi_down_large,
                ord('D'): self._handle_roi_right_large,
                # 数字键选择ROI
                ord('1'): lambda app_state: self._handle_roi_select(0, app_state),
                ord('2'): lambda app_state: self._handle_roi_select(1, app_state),
                ord('3'): lambda app_state: self._handle_roi_select(2, app_state),
                ord('4'): lambda app_state: self._handle_roi_select(3, app_state),
                ord('5'): lambda app_state: self._handle_roi_select(4, app_state),
                ord('6'): lambda app_state: self._handle_roi_select(5, app_state),
            },
            MODE_DETECTION: {
                ord('c'): self._handle_calibration_mode,
                ord('s'): self._handle_save_thresholds,
                ord('l'): self._handle_quick_save_led,
                ord('r'): self._handle_reset_detection,
                # 阈值调整按键
                ord('g'): self._handle_green_threshold_down,
                ord('G'): self._handle_green_threshold_up,
                ord('y'): self._handle_red_threshold_down,
                ord('Y'): self._handle_red_threshold_up,
                ord('d'): self._handle_digit_threshold_down,
                ord('D'): self._handle_digit_threshold_up,
            }
        }
    
    def _process_key(self, key: int, app_state: Any) -> bool:
        """处理按键"""
        # 首先检查全局按键处理器
        if key in self.key_handlers:
            try:
                return self.key_handlers[key](app_state)
            except Exception as e:
                logging.error(f"Global key handler failed for key {key}: {e}")
                return False
        
        # 然后检查模式特定按键处理器
        current_mode = getattr(app_state, 'current_mode', MODE_DETECTION)
        if current_mode in self.mode_key_handlers:
            mode_handlers = self.mode_key_handlers[current_mode]
            if key in mode_handlers:
                try:
                    return mode_handlers[key](app_state)
                except Exception as e:
                    logging.error(f"Mode key handler failed for key {key} in mode {current_mode}: {e}")
                    return False
        
        # 如果没有找到处理器，记录调试信息
        if self.debug_mode:
            logging.debug(f"No handler found for key {key} ('{chr(key) if 32 <= key <= 126 else key}') in mode {current_mode}")
        
        return False
    
    # 全局按键处理器
    def _handle_quit(self, app_state: Any) -> bool:
        """处理退出按键"""
        app_state.running = False
        logging.info("Application quit requested")
        return True
    
    def _handle_window_topmost(self, app_state: Any) -> bool:
        """处理窗口置顶切换"""
        app_state.window_topmost = not app_state.window_topmost
        
        # 导入窗口管理函数
        from ui_handler import toggle_window_topmost
        
        # 尝试多个可能的窗口标题
        window_titles = [
            'Detection Window',
            'LED & Digit Detector - Press \'h\' for help v1.5',
            'LED & Digit Detector',
            'OpenCV Window'
        ]
        
        success = False
        for title in window_titles:
            if toggle_window_topmost(title, app_state.window_topmost):
                success = True
                break
        
        if success:
            status_text = "Window Topmost: ON" if app_state.window_topmost else "Window Topmost: OFF"
            app_state.status_message = status_text
            logging.info(f"Window topmost toggled: {app_state.window_topmost}")
        else:
            app_state.window_topmost = not app_state.window_topmost  # 回退状态
            app_state.status_message = "Failed to toggle topmost: Window not found"
            logging.warning("Failed to toggle window topmost: Could not find window")
        
        return True
    
    def _handle_calibration_mode(self, app_state: Any) -> bool:
        """处理校准模式切换"""
        # 如果正在执行分析流程，不允许切换到校准
        if hasattr(app_state, 'led_analysis_state') and app_state.led_analysis_state != 'IDLE':
            print("Warning: Cannot switch to Calibration mode while analysis process is active.")
            logging.warning("Attempted to switch to Calibration mode during active analysis process.")
            return False
        
        logging.info("Switching to Calibration mode.")
        app_state.current_mode = MODE_CALIBRATION
        app_state.current_calib_state = CALIB_STATE_START
        return True
    
    def _handle_save(self, app_state: Any) -> bool:
        """处理保存按键"""
        # 导入配置管理器
        import config_manager
        
        logging.info("Saving current configuration...")
        if config_manager.save_config(app_state):
            if hasattr(app_state, 'display_frame') and app_state.display_frame is not None:
                cv2.putText(app_state.display_frame, "Configuration Saved!", 
                           (app_state.display_frame.shape[1] // 2 - 100, 
                            app_state.display_frame.shape[0] // 2),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            app_state.status_message = "Configuration saved successfully"
        else:
            app_state.status_message = "Failed to save configuration"
        
        return True
    
    def _handle_help(self, app_state: Any) -> bool:
        """处理帮助按键"""
        print("\n=== 按键帮助 ===")
        print("全局按键:")
        print("  q - 退出程序")
        print("  w - 切换窗口置顶")
        print("  c - 切换到校准模式")
        print("  s - 保存配置")
        print("  h - 显示帮助")
        
        current_mode = getattr(app_state, 'current_mode', MODE_DETECTION)
        if current_mode == MODE_CALIBRATION:
            print("\n校准模式按键:")
            print("  b - 基准点校准")
            print("  l - LED校准")
            print("  d - 数码管校准")
            print("  r - 重置校准")
            print("  t - 模板模式")
            print("  f - 固定模板模式")
            print("  m - 手动模式")
            print("  WASD - ROI微调")
            print("  Shift+WASD - ROI大步长微调")
            print("  1-6 - 选择ROI")
            print("  Enter - 确认")
        elif current_mode == MODE_DETECTION:
            print("\n检测模式按键:")
            print("  g/G - 调整绿色LED阈值")
            print("  y/Y - 调整红色LED阈值")
            print("  d/D - 调整数码管阈值")
            print("  l - 快速保存LED样本")
            print("  r - 重置检测")
        
        print("================\n")
        return True
    
    def _handle_escape(self, app_state: Any) -> bool:
        """处理ESC按键"""
        # 根据当前状态决定ESC的行为
        current_mode = getattr(app_state, 'current_mode', MODE_DETECTION)
        
        if current_mode == MODE_CALIBRATION:
            current_calib_state = getattr(app_state, 'current_calib_state', CALIB_STATE_START)
            
            if current_calib_state == CALIB_STATE_BASE_POINTS_SELECT:
                # 跳过基准点功能
                app_state.alignment_enabled = False
                app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
                print("跳过基准点校准，ROI将使用固定坐标模式")
                return True
            else:
                # 返回校准开始状态
                app_state.current_calib_state = CALIB_STATE_START
                print("返回校准开始状态")
                return True
        
        return False
    
    # ROI微调处理器
    def _handle_roi_up(self, app_state: Any) -> bool:
        """处理ROI向上微调"""
        return self._adjust_current_roi(app_state, 0, -self.roi_fine_tune_step)
    
    def _handle_roi_down(self, app_state: Any) -> bool:
        """处理ROI向下微调"""
        return self._adjust_current_roi(app_state, 0, self.roi_fine_tune_step)
    
    def _handle_roi_left(self, app_state: Any) -> bool:
        """处理ROI向左微调"""
        return self._adjust_current_roi(app_state, -self.roi_fine_tune_step, 0)
    
    def _handle_roi_right(self, app_state: Any) -> bool:
        """处理ROI向右微调"""
        return self._adjust_current_roi(app_state, self.roi_fine_tune_step, 0)
    
    def _handle_roi_up_large(self, app_state: Any) -> bool:
        """处理ROI向上大步长微调"""
        return self._adjust_current_roi(app_state, 0, -self.roi_fine_tune_large_step)
    
    def _handle_roi_down_large(self, app_state: Any) -> bool:
        """处理ROI向下大步长微调"""
        return self._adjust_current_roi(app_state, 0, self.roi_fine_tune_large_step)
    
    def _handle_roi_left_large(self, app_state: Any) -> bool:
        """处理ROI向左大步长微调"""
        return self._adjust_current_roi(app_state, -self.roi_fine_tune_large_step, 0)
    
    def _handle_roi_right_large(self, app_state: Any) -> bool:
        """处理ROI向右大步长微调"""
        return self._adjust_current_roi(app_state, self.roi_fine_tune_large_step, 0)
    
    def _adjust_current_roi(self, app_state: Any, dx: int, dy: int) -> bool:
        """调整当前ROI位置"""
        if not hasattr(app_state, 'current_rect') or app_state.current_rect is None:
            return False
        
        x, y, w, h = app_state.current_rect
        new_x = max(0, x + dx)
        new_y = max(0, y + dy)
        
        # 确保ROI不超出边界（如果有帧信息）
        if hasattr(app_state, 'current_frame') and app_state.current_frame is not None:
            frame_h, frame_w = app_state.current_frame.shape[:2]
            new_x = min(new_x, frame_w - w)
            new_y = min(new_y, frame_h - h)
        
        app_state.current_rect = (new_x, new_y, w, h)
        
        if self.debug_mode:
            logging.debug(f"ROI adjusted by ({dx}, {dy}) to ({new_x}, {new_y}, {w}, {h})")
        
        return True
    
    def _handle_roi_select(self, roi_index: int, app_state: Any) -> bool:
        """处理ROI选择"""
        if (hasattr(app_state, 'led_rois') and 
            roi_index < len(app_state.led_rois) and 
            app_state.led_rois[roi_index] is not None):
            
            app_state.selected_roi_index = roi_index
            app_state.moving_roi = False  # 重置移动状态
            app_state.current_rect = app_state.led_rois[roi_index]
            
            is_green = roi_index < getattr(app_state, 'led_num_green', 0)
            led_label = (f"G{roi_index+1}" if is_green 
                        else f"R{roi_index - app_state.led_num_green + 1}")
            print(f"选中 {led_label} ROI，可以拖拽移动或使用 WASD 微调")
            return True
        else:
            print(f"ROI {roi_index + 1} 不存在或未定义")
            return False
    
    # 相机设置处理器（简化版，主要逻辑在状态机中）
    def _handle_resolution_down(self, app_state: Any) -> bool:
        """处理分辨率降低"""
        # 实际逻辑在CameraStateMachine中处理
        return True
    
    def _handle_resolution_up(self, app_state: Any) -> bool:
        """处理分辨率提高"""
        # 实际逻辑在CameraStateMachine中处理
        return True
    
    def _handle_exposure_down(self, app_state: Any) -> bool:
        """处理曝光降低"""
        # 实际逻辑在CameraStateMachine中处理
        return True
    
    def _handle_exposure_up(self, app_state: Any) -> bool:
        """处理曝光提高"""
        # 实际逻辑在CameraStateMachine中处理
        return True
    
    def _handle_brightness_down(self, app_state: Any) -> bool:
        """处理亮度降低"""
        # 实际逻辑在CameraStateMachine中处理
        return True
    
    def _handle_brightness_up(self, app_state: Any) -> bool:
        """处理亮度提高"""
        # 实际逻辑在CameraStateMachine中处理
        return True
    
    def _handle_gain_down(self, app_state: Any) -> bool:
        """处理增益降低"""
        # 实际逻辑在CameraStateMachine中处理
        return True
    
    def _handle_gain_up(self, app_state: Any) -> bool:
        """处理增益提高"""
        # 实际逻辑在CameraStateMachine中处理
        return True
    
    def _handle_reset_camera(self, app_state: Any) -> bool:
        """处理相机重置"""
        # 实际逻辑在CameraStateMachine中处理
        return True
    
    def _handle_camera_confirm(self, app_state: Any) -> bool:
        """处理相机设置确认"""
        # 实际逻辑在CameraStateMachine中处理
        return True
    
    # 校准处理器（简化版，主要逻辑在状态机中）
    def _handle_base_point_calibration(self, app_state: Any) -> bool:
        """处理基准点校准"""
        # 实际逻辑在CalibrationStateMachine中处理
        return True
    
    def _handle_led_calibration(self, app_state: Any) -> bool:
        """处理LED校准"""
        # 实际逻辑在CalibrationStateMachine中处理
        return True
    
    def _handle_digit_calibration(self, app_state: Any) -> bool:
        """处理数码管校准"""
        # 实际逻辑在CalibrationStateMachine中处理
        return True
    
    def _handle_reset_calibration(self, app_state: Any) -> bool:
        """处理校准重置"""
        # 实际逻辑在CalibrationStateMachine中处理
        return True
    
    def _handle_next_roi(self, app_state: Any) -> bool:
        """处理下一个ROI"""
        # 实际逻辑在CalibrationStateMachine中处理
        return True
    
    def _handle_previous_roi(self, app_state: Any) -> bool:
        """处理上一个ROI"""
        # 实际逻辑在CalibrationStateMachine中处理
        return True
    
    def _handle_template_mode(self, app_state: Any) -> bool:
        """处理模板模式"""
        # 实际逻辑在CalibrationStateMachine中处理
        return True
    
    def _handle_fixed_template_mode(self, app_state: Any) -> bool:
        """处理固定模板模式"""
        # 实际逻辑在CalibrationStateMachine中处理
        return True
    
    def _handle_manual_mode(self, app_state: Any) -> bool:
        """处理手动模式"""
        # 实际逻辑在CalibrationStateMachine中处理
        return True
    
    def _handle_calibration_confirm(self, app_state: Any) -> bool:
        """处理校准确认"""
        # 实际逻辑在CalibrationStateMachine中处理
        return True
    
    # 检测模式处理器（简化版，主要逻辑在状态机中）
    def _handle_save_thresholds(self, app_state: Any) -> bool:
        """处理阈值保存"""
        # 实际逻辑在DetectionStateMachine中处理
        return True
    
    def _handle_quick_save_led(self, app_state: Any) -> bool:
        """处理LED快速保存"""
        # 实际逻辑在DetectionStateMachine中处理
        return True
    
    def _handle_reset_detection(self, app_state: Any) -> bool:
        """处理检测重置"""
        # 实际逻辑在DetectionStateMachine中处理
        return True
    
    def _handle_green_threshold_down(self, app_state: Any) -> bool:
        """处理绿色阈值降低"""
        # 实际逻辑在DetectionStateMachine中处理
        return True
    
    def _handle_green_threshold_up(self, app_state: Any) -> bool:
        """处理绿色阈值提高"""
        # 实际逻辑在DetectionStateMachine中处理
        return True
    
    def _handle_red_threshold_down(self, app_state: Any) -> bool:
        """处理红色阈值降低"""
        # 实际逻辑在DetectionStateMachine中处理
        return True
    
    def _handle_red_threshold_up(self, app_state: Any) -> bool:
        """处理红色阈值提高"""
        # 实际逻辑在DetectionStateMachine中处理
        return True
    
    def _handle_digit_threshold_down(self, app_state: Any) -> bool:
        """处理数码管阈值降低"""
        # 实际逻辑在DetectionStateMachine中处理
        return True
    
    def _handle_digit_threshold_up(self, app_state: Any) -> bool:
        """处理数码管阈值提高"""
        # 实际逻辑在DetectionStateMachine中处理
        return True
    
    def add_key_handler(self, key: int, handler: Callable, mode: Optional[int] = None) -> bool:
        """
        添加按键处理器
        
        Args:
            key: 按键码
            handler: 处理函数
            mode: 模式（None表示全局）
            
        Returns:
            是否成功添加
        """
        if not callable(handler):
            logging.error("Key handler must be callable")
            return False
        
        if mode is None:
            self.key_handlers[key] = handler
            logging.debug(f"Added global key handler for key {key}")
        else:
            if mode not in self.mode_key_handlers:
                self.mode_key_handlers[mode] = {}
            self.mode_key_handlers[mode][key] = handler
            logging.debug(f"Added mode-specific key handler for key {key} in mode {mode}")
        
        return True
    
    def remove_key_handler(self, key: int, mode: Optional[int] = None) -> bool:
        """
        移除按键处理器
        
        Args:
            key: 按键码
            mode: 模式（None表示全局）
            
        Returns:
            是否成功移除
        """
        if mode is None:
            if key in self.key_handlers:
                del self.key_handlers[key]
                logging.debug(f"Removed global key handler for key {key}")
                return True
        else:
            if mode in self.mode_key_handlers and key in self.mode_key_handlers[mode]:
                del self.mode_key_handlers[mode][key]
                logging.debug(f"Removed mode-specific key handler for key {key} in mode {mode}")
                return True
        
        return False
    
    def validate_event_data(self, event_type: EventType, event_data: Dict[str, Any]) -> bool:
        """验证键盘事件数据"""
        if not super().validate_event_data(event_type, event_data):
            return False
        
        # 检查必需的键盘事件字段
        if 'key' not in event_data:
            logging.error("Missing required field 'key' in keyboard event data")
            return False
        
        key = event_data['key']
        if not isinstance(key, int) or key < 0 or key > 255:
            logging.error(f"Invalid key code: {key}")
            return False
        
        return True
    
    def get_keyboard_state(self) -> Dict[str, Any]:
        """获取当前键盘状态"""
        return {
            'last_key': self.last_key,
            'last_key_time': self.last_key_time,
            'key_repeat_delay': self.key_repeat_delay,
            'roi_fine_tune_step': self.roi_fine_tune_step,
            'roi_fine_tune_large_step': self.roi_fine_tune_large_step,
            'global_handlers_count': len(self.key_handlers),
            'mode_handlers_count': sum(len(handlers) for handlers in self.mode_key_handlers.values())
        }
    
    def cleanup(self):
        """清理资源"""
        self.key_handlers.clear()
        self.mode_key_handlers.clear()
        self.last_key = -1
        self.last_key_time = 0
        super().cleanup()
        logging.debug("KeyboardEventHandler cleaned up")
