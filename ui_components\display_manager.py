"""
显示管理器

负责协调多个渲染器的工作，管理帧的准备和最终显示。
提供统一的渲染接口，支持渲染器的动态添加和移除。
"""

import cv2
import numpy as np
import time
from typing import List, Dict, Any, Optional, Tuple
import logging

from .base_renderer import BaseRenderer


class DisplayManager:
    """显示管理器"""
    
    def __init__(self):
        """初始化显示管理器"""
        self.renderers: List[BaseRenderer] = []
        self.renderer_map: Dict[str, BaseRenderer] = {}
        
        # 性能统计
        self.total_render_time = 0.0
        self.render_count = 0
        self.last_render_time = 0.0
        
        # 错误处理
        self.error_frame_cache = None
        self.last_error_time = 0
        self.error_display_duration = 3.0  # 错误显示持续时间（秒）
        
        # 帧准备配置
        self.frame_copy_enabled = True
        self.frame_validation_enabled = True
        
        logging.info("DisplayManager initialized")
    
    def add_renderer(self, renderer: BaseRenderer) -> bool:
        """
        添加渲染器
        
        Args:
            renderer: 要添加的渲染器
            
        Returns:
            是否成功添加
        """
        if not isinstance(renderer, BaseRenderer):
            logging.error(f"Invalid renderer type: {type(renderer)}")
            return False
        
        if renderer.name in self.renderer_map:
            logging.warning(f"Renderer '{renderer.name}' already exists, replacing")
            self.remove_renderer(renderer.name)
        
        self.renderers.append(renderer)
        self.renderer_map[renderer.name] = renderer
        
        logging.info(f"Renderer '{renderer.name}' added")
        return True
    
    def remove_renderer(self, name: str) -> bool:
        """
        移除渲染器
        
        Args:
            name: 渲染器名称
            
        Returns:
            是否成功移除
        """
        if name not in self.renderer_map:
            logging.warning(f"Renderer '{name}' not found")
            return False
        
        renderer = self.renderer_map[name]
        self.renderers.remove(renderer)
        del self.renderer_map[name]
        
        # 清理渲染器资源
        renderer.cleanup()
        
        logging.info(f"Renderer '{name}' removed")
        return True
    
    def get_renderer(self, name: str) -> Optional[BaseRenderer]:
        """
        获取渲染器
        
        Args:
            name: 渲染器名称
            
        Returns:
            渲染器实例或None
        """
        return self.renderer_map.get(name)
    
    def list_renderers(self) -> List[str]:
        """
        列出所有渲染器名称
        
        Returns:
            渲染器名称列表
        """
        return list(self.renderer_map.keys())
    
    def enable_renderer(self, name: str, enabled: bool = True) -> bool:
        """
        启用/禁用渲染器
        
        Args:
            name: 渲染器名称
            enabled: 是否启用
            
        Returns:
            是否成功设置
        """
        renderer = self.get_renderer(name)
        if renderer is None:
            return False
        
        renderer.set_enabled(enabled)
        return True
    
    def prepare_frame(self, source_frame: np.ndarray, app_state: Any) -> np.ndarray:
        """
        准备显示帧
        
        Args:
            source_frame: 源帧
            app_state: 应用状态
            
        Returns:
            准备好的显示帧
        """
        if source_frame is None:
            return self._create_error_frame("No source frame available")
        
        # 验证帧
        if self.frame_validation_enabled and not self._validate_frame(source_frame):
            return self._create_error_frame("Invalid frame format")
        
        # 复制帧（避免修改原始帧）
        if self.frame_copy_enabled:
            display_frame = source_frame.copy()
        else:
            display_frame = source_frame
        
        # 设置到app_state中
        app_state.display_frame = display_frame
        
        return display_frame
    
    def render_all(self, frame: np.ndarray, app_state: Any) -> np.ndarray:
        """
        使用所有启用的渲染器渲染帧
        
        Args:
            frame: 输入帧
            app_state: 应用状态
            
        Returns:
            渲染后的帧
        """
        start_time = time.time()
        
        try:
            # 准备帧
            current_frame = self.prepare_frame(frame, app_state)
            
            # 依次应用所有启用的渲染器
            for renderer in self.renderers:
                if renderer.enabled:
                    try:
                        current_frame = renderer.render_with_timing(current_frame, app_state)
                    except Exception as e:
                        logging.error(f"Renderer '{renderer.name}' failed: {e}")
                        # 继续处理其他渲染器
                        continue
            
            # 更新性能统计
            render_time = time.time() - start_time
            self.last_render_time = render_time
            self.total_render_time += render_time
            self.render_count += 1
            
            return current_frame
            
        except Exception as e:
            logging.error(f"DisplayManager render failed: {e}")
            return self._create_error_frame(f"Render error: {str(e)}")
    
    def render_with_specific_renderers(self, frame: np.ndarray, app_state: Any, 
                                     renderer_names: List[str]) -> np.ndarray:
        """
        使用指定的渲染器渲染帧
        
        Args:
            frame: 输入帧
            app_state: 应用状态
            renderer_names: 要使用的渲染器名称列表
            
        Returns:
            渲染后的帧
        """
        start_time = time.time()
        
        try:
            # 准备帧
            current_frame = self.prepare_frame(frame, app_state)
            
            # 应用指定的渲染器
            for name in renderer_names:
                renderer = self.get_renderer(name)
                if renderer and renderer.enabled:
                    try:
                        current_frame = renderer.render_with_timing(current_frame, app_state)
                    except Exception as e:
                        logging.error(f"Renderer '{name}' failed: {e}")
                        continue
            
            # 更新性能统计
            render_time = time.time() - start_time
            self.last_render_time = render_time
            self.total_render_time += render_time
            self.render_count += 1
            
            return current_frame
            
        except Exception as e:
            logging.error(f"DisplayManager specific render failed: {e}")
            return self._create_error_frame(f"Render error: {str(e)}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            性能统计字典
        """
        avg_render_time = (self.total_render_time / self.render_count 
                          if self.render_count > 0 else 0)
        
        # 收集所有渲染器的统计信息
        renderer_stats = {}
        for renderer in self.renderers:
            renderer_stats[renderer.name] = renderer.get_performance_stats()
        
        return {
            'total_render_time': self.total_render_time,
            'render_count': self.render_count,
            'average_render_time': avg_render_time,
            'last_render_time': self.last_render_time,
            'fps': 1.0 / avg_render_time if avg_render_time > 0 else 0,
            'renderer_count': len(self.renderers),
            'enabled_renderer_count': sum(1 for r in self.renderers if r.enabled),
            'renderer_stats': renderer_stats
        }
    
    def reset_performance_stats(self):
        """重置性能统计"""
        self.total_render_time = 0.0
        self.render_count = 0
        self.last_render_time = 0.0
        
        # 重置所有渲染器的统计
        for renderer in self.renderers:
            renderer.reset_performance_stats()
        
        logging.info("DisplayManager performance stats reset")
    
    def _validate_frame(self, frame: np.ndarray) -> bool:
        """验证帧的有效性"""
        if frame is None:
            return False
        
        if not isinstance(frame, np.ndarray):
            return False
        
        if len(frame.shape) != 3:
            return False
        
        if frame.shape[2] != 3:  # 期望BGR格式
            return False
        
        return True
    
    def _create_error_frame(self, error_message: str) -> np.ndarray:
        """
        创建错误显示帧
        
        Args:
            error_message: 错误信息
            
        Returns:
            错误帧
        """
        current_time = time.time()
        
        # 检查是否需要更新错误帧缓存
        if (self.error_frame_cache is None or 
            (current_time - self.last_error_time) > self.error_display_duration):
            
            # 创建错误帧
            error_frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 绘制错误信息
            cv2.putText(error_frame, "DISPLAY ERROR", (50, 200), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 2)
            cv2.putText(error_frame, error_message, (50, 250), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 1)
            cv2.putText(error_frame, f"Time: {time.strftime('%H:%M:%S')}", (50, 300), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (128, 128, 128), 1)
            
            self.error_frame_cache = error_frame
            self.last_error_time = current_time
        
        return self.error_frame_cache.copy()
    
    def set_frame_copy_enabled(self, enabled: bool):
        """设置是否启用帧复制"""
        self.frame_copy_enabled = enabled
        logging.info(f"Frame copy {'enabled' if enabled else 'disabled'}")
    
    def set_frame_validation_enabled(self, enabled: bool):
        """设置是否启用帧验证"""
        self.frame_validation_enabled = enabled
        logging.info(f"Frame validation {'enabled' if enabled else 'disabled'}")
    
    def cleanup(self):
        """清理资源"""
        # 清理所有渲染器
        for renderer in self.renderers:
            renderer.cleanup()
        
        self.renderers.clear()
        self.renderer_map.clear()
        
        # 清理缓存
        self.error_frame_cache = None
        
        # 重置统计
        self.reset_performance_stats()
        
        logging.info("DisplayManager cleaned up")
    
    def __str__(self) -> str:
        """字符串表示"""
        enabled_count = sum(1 for r in self.renderers if r.enabled)
        return f"DisplayManager(renderers={len(self.renderers)}, enabled={enabled_count})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        stats = self.get_performance_stats()
        return (f"DisplayManager(renderers={len(self.renderers)}, "
                f"enabled={stats['enabled_renderer_count']}, "
                f"renders={stats['render_count']}, "
                f"avg_time={stats['average_render_time']:.3f}s)")
