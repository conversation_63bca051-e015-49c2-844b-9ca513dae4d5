"""
事件管理器

负责协调多个事件处理器的工作，管理事件的分发和路由。
提供统一的事件处理接口，支持事件处理器的动态添加和移除。
"""

import cv2
import time
from typing import List, Dict, Any, Optional, Callable
import logging

from .base_event_handler import BaseEventHandler, EventType, EventPriority


class EventManager:
    """事件管理器"""
    
    def __init__(self):
        """初始化事件管理器"""
        self.handlers: List[BaseEventHandler] = []
        self.handler_map: Dict[str, BaseEventHandler] = {}
        self.priority_handlers: Dict[EventPriority, List[BaseEventHandler]] = {
            priority: [] for priority in EventPriority
        }
        
        # 共享状态管理（用于多线程模式）
        self._shared_state = None
        
        # 事件队列和处理
        self.event_queue: List[Dict[str, Any]] = []
        self.max_queue_size = 1000
        self.process_events_immediately = True
        
        # 性能统计
        self.total_events_processed = 0
        self.total_processing_time = 0.0
        self.last_processing_time = 0.0
        
        # 错误处理
        self.error_handlers: List[Callable] = []
        self.max_errors_per_handler = 10
        
        logging.info("EventManager initialized")
    
    def add_handler(self, handler: BaseEventHandler, 
                   priority: EventPriority = EventPriority.NORMAL) -> bool:
        """
        添加事件处理器
        
        Args:
            handler: 要添加的事件处理器
            priority: 处理器优先级
            
        Returns:
            是否成功添加
        """
        if not isinstance(handler, BaseEventHandler):
            logging.error(f"Invalid handler type: {type(handler)}")
            return False
        
        if handler.name in self.handler_map:
            logging.warning(f"Handler '{handler.name}' already exists, replacing")
            self.remove_handler(handler.name)
        
        self.handlers.append(handler)
        self.handler_map[handler.name] = handler
        self.priority_handlers[priority].append(handler)
        
        logging.info(f"Handler '{handler.name}' added with priority {priority.name}")
        return True
    
    def remove_handler(self, name: str) -> bool:
        """
        移除事件处理器
        
        Args:
            name: 处理器名称
            
        Returns:
            是否成功移除
        """
        if name not in self.handler_map:
            logging.warning(f"Handler '{name}' not found")
            return False
        
        handler = self.handler_map[name]
        
        # 从所有列表中移除
        self.handlers.remove(handler)
        del self.handler_map[name]
        
        for priority_list in self.priority_handlers.values():
            if handler in priority_list:
                priority_list.remove(handler)
        
        # 清理处理器资源
        handler.cleanup()
        
        logging.info(f"Handler '{name}' removed")
        return True
    
    def get_handler(self, name: str) -> Optional[BaseEventHandler]:
        """
        获取事件处理器
        
        Args:
            name: 处理器名称
            
        Returns:
            处理器实例或None
        """
        return self.handler_map.get(name)
    
    def list_handlers(self) -> List[str]:
        """
        列出所有处理器名称
        
        Returns:
            处理器名称列表
        """
        return list(self.handler_map.keys())
    
    def enable_handler(self, name: str, enabled: bool = True) -> bool:
        """
        启用/禁用事件处理器
        
        Args:
            name: 处理器名称
            enabled: 是否启用
            
        Returns:
            是否成功设置
        """
        handler = self.get_handler(name)
        if handler is None:
            return False
        
        handler.set_enabled(enabled)
        return True
    
    def set_shared_state(self, shared_state: Any):
        """
        设置共享状态（用于多线程模式）
        
        Args:
            shared_state: 共享状态对象
        """
        self._shared_state = shared_state
        logging.debug(f"Shared state set: {shared_state is not None}")
    
    def get_key(self) -> int:
        """
        获取按键，兼容单线程和多线程模式
        
        Returns:
            按键码，-1表示无按键
        """
        if self._shared_state is not None:
            # 多线程模式：从共享状态获取按键
            return self._shared_state.get_and_clear_key()
        else:
            # 单线程模式：直接调用 waitKey
            return cv2.waitKey(1) & 0xFF
    
    def setup_mouse_callback(self, window_name: str, app_state: Any) -> bool:
        """
        设置鼠标回调
        
        Args:
            window_name: 窗口名称
            app_state: 应用状态
            
        Returns:
            是否成功设置
        """
        try:
            # 创建鼠标回调函数
            def mouse_callback(event, x, y, flags, param):
                self._handle_mouse_event(event, x, y, flags, param, app_state)
            
            cv2.setMouseCallback(window_name, mouse_callback)
            logging.debug(f"Mouse callback set for window '{window_name}'")
            return True
            
        except Exception as e:
            logging.error(f"Failed to setup mouse callback: {e}")
            return False
    
    def _handle_mouse_event(self, cv_event: int, x: int, y: int, flags: int, 
                           param: Any, app_state: Any):
        """
        处理OpenCV鼠标事件
        
        Args:
            cv_event: OpenCV事件类型
            x, y: 鼠标坐标
            flags: 事件标志
            param: 参数（未使用）
            app_state: 应用状态
        """
        # 转换OpenCV事件到内部事件类型
        event_type = self._convert_cv_mouse_event(cv_event)
        if event_type is None:
            return
        
        # 构建事件数据
        event_data = {
            'x': x,
            'y': y,
            'flags': flags,
            'cv_event': cv_event,
            'timestamp': time.time()
        }
        
        # 分发事件
        self.dispatch_event(event_type, event_data, app_state)
    
    def _convert_cv_mouse_event(self, cv_event: int) -> Optional[EventType]:
        """
        转换OpenCV鼠标事件到内部事件类型
        
        Args:
            cv_event: OpenCV事件类型
            
        Returns:
            内部事件类型或None
        """
        event_map = {
            cv2.EVENT_LBUTTONDOWN: EventType.MOUSE_CLICK,
            cv2.EVENT_RBUTTONDOWN: EventType.MOUSE_CLICK,
            cv2.EVENT_MBUTTONDOWN: EventType.MOUSE_CLICK,
            cv2.EVENT_LBUTTONUP: EventType.MOUSE_RELEASE,
            cv2.EVENT_RBUTTONUP: EventType.MOUSE_RELEASE,
            cv2.EVENT_MBUTTONUP: EventType.MOUSE_RELEASE,
            cv2.EVENT_MOUSEMOVE: EventType.MOUSE_MOVE,
        }
        
        return event_map.get(cv_event)
    
    def dispatch_event(self, event_type: EventType, event_data: Dict[str, Any], 
                      app_state: Any) -> bool:
        """
        分发事件到合适的处理器
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
            app_state: 应用状态
            
        Returns:
            是否有处理器成功处理了事件
        """
        if self.process_events_immediately:
            return self._process_event_immediately(event_type, event_data, app_state)
        else:
            return self._queue_event(event_type, event_data, app_state)
    
    def _process_event_immediately(self, event_type: EventType, event_data: Dict[str, Any], 
                                  app_state: Any) -> bool:
        """
        立即处理事件
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
            app_state: 应用状态
            
        Returns:
            是否有处理器成功处理了事件
        """
        start_time = time.time()
        handled = False
        
        try:
            # 按优先级顺序处理
            for priority in [EventPriority.CRITICAL, EventPriority.HIGH, 
                           EventPriority.NORMAL, EventPriority.LOW]:
                for handler in self.priority_handlers[priority]:
                    if handler.enabled and handler.can_handle_event(event_type, app_state):
                        try:
                            if handler.process_event(event_type, event_data, app_state):
                                handled = True
                                # 高优先级处理器处理后可能需要停止传播
                                if priority in [EventPriority.CRITICAL, EventPriority.HIGH]:
                                    break
                        except Exception as e:
                            logging.error(f"Handler '{handler.name}' failed to process event: {e}")
                            self._handle_handler_error(handler, e)
                
                # 如果高优先级处理器已处理，停止传播
                if handled and priority in [EventPriority.CRITICAL, EventPriority.HIGH]:
                    break
            
            # 更新统计
            processing_time = time.time() - start_time
            self.last_processing_time = processing_time
            self.total_processing_time += processing_time
            self.total_events_processed += 1
            
            return handled
            
        except Exception as e:
            logging.error(f"Event dispatch failed: {e}")
            return False
    
    def _queue_event(self, event_type: EventType, event_data: Dict[str, Any], 
                    app_state: Any) -> bool:
        """
        将事件加入队列
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
            app_state: 应用状态
            
        Returns:
            是否成功加入队列
        """
        if len(self.event_queue) >= self.max_queue_size:
            logging.warning("Event queue full, dropping oldest event")
            self.event_queue.pop(0)
        
        event = {
            'type': event_type,
            'data': event_data,
            'app_state': app_state,
            'timestamp': time.time()
        }
        
        self.event_queue.append(event)
        return True
    
    def process_queued_events(self) -> int:
        """
        处理队列中的事件
        
        Returns:
            处理的事件数量
        """
        processed_count = 0
        
        while self.event_queue:
            event = self.event_queue.pop(0)
            
            if self._process_event_immediately(
                event['type'], event['data'], event['app_state']
            ):
                processed_count += 1
        
        return processed_count
    
    def _handle_handler_error(self, handler: BaseEventHandler, error: Exception):
        """
        处理处理器错误
        
        Args:
            handler: 出错的处理器
            error: 错误信息
        """
        # 调用错误处理器
        for error_handler in self.error_handlers:
            try:
                error_handler(handler, error)
            except Exception as e:
                logging.error(f"Error handler failed: {e}")
        
        # 如果错误过多，禁用处理器
        if handler.consecutive_errors >= self.max_errors_per_handler:
            handler.set_enabled(False)
            logging.warning(f"Handler '{handler.name}' disabled due to too many errors")
    
    def add_error_handler(self, error_handler: Callable):
        """
        添加错误处理器
        
        Args:
            error_handler: 错误处理函数
        """
        if callable(error_handler):
            self.error_handlers.append(error_handler)
            logging.debug("Error handler added")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            性能统计字典
        """
        avg_processing_time = (self.total_processing_time / self.total_events_processed 
                              if self.total_events_processed > 0 else 0)
        
        # 收集所有处理器的统计信息
        handler_stats = {}
        for handler in self.handlers:
            handler_stats[handler.name] = handler.get_performance_stats()
        
        return {
            'total_events_processed': self.total_events_processed,
            'total_processing_time': self.total_processing_time,
            'average_processing_time': avg_processing_time,
            'last_processing_time': self.last_processing_time,
            'events_per_second': (self.total_events_processed / self.total_processing_time 
                                 if self.total_processing_time > 0 else 0),
            'handler_count': len(self.handlers),
            'enabled_handler_count': sum(1 for h in self.handlers if h.enabled),
            'queue_size': len(self.event_queue),
            'max_queue_size': self.max_queue_size,
            'handler_stats': handler_stats
        }
    
    def reset_performance_stats(self):
        """重置性能统计"""
        self.total_events_processed = 0
        self.total_processing_time = 0.0
        self.last_processing_time = 0.0
        
        # 重置所有处理器的统计
        for handler in self.handlers:
            handler.reset_performance_stats()
        
        logging.info("EventManager performance stats reset")
    
    def cleanup(self):
        """清理资源"""
        # 清理所有处理器
        for handler in self.handlers:
            handler.cleanup()
        
        self.handlers.clear()
        self.handler_map.clear()
        for priority_list in self.priority_handlers.values():
            priority_list.clear()
        
        # 清理队列
        self.event_queue.clear()
        self.error_handlers.clear()
        
        # 重置统计
        self.reset_performance_stats()
        
        logging.info("EventManager cleaned up")
    
    def __str__(self) -> str:
        """字符串表示"""
        enabled_count = sum(1 for h in self.handlers if h.enabled)
        return f"EventManager(handlers={len(self.handlers)}, enabled={enabled_count})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        stats = self.get_performance_stats()
        return (f"EventManager(handlers={len(self.handlers)}, "
                f"enabled={stats['enabled_handler_count']}, "
                f"events={stats['total_events_processed']})")
