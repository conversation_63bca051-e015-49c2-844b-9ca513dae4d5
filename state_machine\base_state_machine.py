"""
状态机基类

提供状态机的基础功能，包括状态转换、条件检查、事件处理等。
所有具体的状态机都应该继承这个基类。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable
import logging
import time


class StateTransition:
    """状态转换定义"""
    
    def __init__(self, from_state: str, to_state: str, condition: Optional[Callable] = None, action: Optional[Callable] = None):
        """
        初始化状态转换
        
        Args:
            from_state: 源状态
            to_state: 目标状态
            condition: 转换条件函数，返回bool
            action: 转换时执行的动作函数
        """
        self.from_state = from_state
        self.to_state = to_state
        self.condition = condition
        self.action = action
    
    def can_transition(self, context: Any = None) -> bool:
        """检查是否可以进行转换"""
        if self.condition is None:
            return True
        try:
            return self.condition(context)
        except Exception as e:
            logging.error(f"State transition condition check failed: {e}")
            return False
    
    def execute_action(self, context: Any = None):
        """执行转换动作"""
        if self.action is not None:
            try:
                self.action(context)
            except Exception as e:
                logging.error(f"State transition action failed: {e}")


class BaseStateMachine(ABC):
    """状态机基类"""
    
    def __init__(self, initial_state: str, name: str = "StateMachine"):
        """
        初始化状态机
        
        Args:
            initial_state: 初始状态
            name: 状态机名称，用于日志
        """
        self.name = name
        self.current_state = initial_state
        self.previous_state = None
        self.state_enter_time = time.time()
        
        # 状态转换表：{from_state: [StateTransition, ...]}
        self.transitions: Dict[str, List[StateTransition]] = {}
        
        # 状态处理器：{state: handler_function}
        self.state_handlers: Dict[str, Callable] = {}
        
        # 状态进入/退出处理器
        self.enter_handlers: Dict[str, Callable] = {}
        self.exit_handlers: Dict[str, Callable] = {}
        
        # 状态历史
        self.state_history: List[tuple] = [(initial_state, time.time())]
        
        # 初始化状态机
        self.setup_transitions()
        self.setup_handlers()
        
        logging.info(f"StateMachine '{self.name}' initialized with state: {initial_state}")
    
    @abstractmethod
    def setup_transitions(self):
        """设置状态转换规则 - 子类必须实现"""
        pass
    
    @abstractmethod
    def setup_handlers(self):
        """设置状态处理器 - 子类必须实现"""
        pass
    
    def add_transition(self, transition: StateTransition):
        """添加状态转换"""
        if transition.from_state not in self.transitions:
            self.transitions[transition.from_state] = []
        self.transitions[transition.from_state].append(transition)
        
        logging.debug(f"Added transition: {transition.from_state} -> {transition.to_state}")
    
    def add_state_handler(self, state: str, handler: Callable):
        """添加状态处理器"""
        self.state_handlers[state] = handler
        logging.debug(f"Added handler for state: {state}")
    
    def add_enter_handler(self, state: str, handler: Callable):
        """添加状态进入处理器"""
        self.enter_handlers[state] = handler
        logging.debug(f"Added enter handler for state: {state}")
    
    def add_exit_handler(self, state: str, handler: Callable):
        """添加状态退出处理器"""
        self.exit_handlers[state] = handler
        logging.debug(f"Added exit handler for state: {state}")
    
    def can_transition_to(self, to_state: str, context: Any = None) -> bool:
        """检查是否可以转换到目标状态"""
        if self.current_state not in self.transitions:
            return False
        
        for transition in self.transitions[self.current_state]:
            if transition.to_state == to_state:
                return transition.can_transition(context)
        
        return False
    
    def transition_to(self, new_state: str, context: Any = None) -> bool:
        """转换到新状态"""
        if not self.can_transition_to(new_state, context):
            logging.warning(f"Cannot transition from {self.current_state} to {new_state}")
            return False
        
        # 找到匹配的转换
        transition = None
        for trans in self.transitions.get(self.current_state, []):
            if trans.to_state == new_state and trans.can_transition(context):
                transition = trans
                break
        
        if transition is None:
            return False
        
        # 执行状态转换
        old_state = self.current_state
        
        # 调用退出处理器
        if old_state in self.exit_handlers:
            try:
                self.exit_handlers[old_state](context)
            except Exception as e:
                logging.error(f"Exit handler for {old_state} failed: {e}")
        
        # 执行转换动作
        transition.execute_action(context)
        
        # 更新状态
        self.previous_state = old_state
        self.current_state = new_state
        self.state_enter_time = time.time()
        
        # 记录状态历史
        self.state_history.append((new_state, self.state_enter_time))
        
        # 调用进入处理器
        if new_state in self.enter_handlers:
            try:
                self.enter_handlers[new_state](context)
            except Exception as e:
                logging.error(f"Enter handler for {new_state} failed: {e}")
        
        # 调用状态改变回调
        self.on_state_changed(old_state, new_state, context)
        
        logging.info(f"StateMachine '{self.name}': {old_state} -> {new_state}")
        return True
    
    def process_state(self, context: Any = None) -> bool:
        """处理当前状态"""
        if self.current_state in self.state_handlers:
            try:
                return self.state_handlers[self.current_state](context)
            except Exception as e:
                logging.error(f"State handler for {self.current_state} failed: {e}")
                return False
        else:
            logging.warning(f"No handler for state: {self.current_state}")
            return True
    
    def on_state_changed(self, old_state: str, new_state: str, context: Any = None):
        """状态改变时的回调 - 子类可以重写"""
        pass
    
    def get_current_state(self) -> str:
        """获取当前状态"""
        return self.current_state
    
    def get_previous_state(self) -> Optional[str]:
        """获取前一个状态"""
        return self.previous_state
    
    def get_state_duration(self) -> float:
        """获取当前状态持续时间（秒）"""
        return time.time() - self.state_enter_time
    
    def get_available_transitions(self) -> List[str]:
        """获取当前状态可用的转换目标"""
        if self.current_state not in self.transitions:
            return []
        
        return [trans.to_state for trans in self.transitions[self.current_state]]
    
    def get_state_history(self) -> List[tuple]:
        """获取状态历史"""
        return self.state_history.copy()
    
    def reset_to_initial(self, initial_state: str = None):
        """重置到初始状态"""
        if initial_state is None:
            initial_state = self.state_history[0][0] if self.state_history else self.current_state
        
        old_state = self.current_state
        self.current_state = initial_state
        self.previous_state = None
        self.state_enter_time = time.time()
        self.state_history = [(initial_state, self.state_enter_time)]
        
        logging.info(f"StateMachine '{self.name}' reset: {old_state} -> {initial_state}")
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"StateMachine(name='{self.name}', current_state='{self.current_state}')"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"StateMachine(name='{self.name}', current_state='{self.current_state}', "
                f"previous_state='{self.previous_state}', duration={self.get_state_duration():.2f}s)")
