# 模块化架构设计文档

## 设计时间
2025-08-27

## 1. 架构设计原则

### 1.1 SOLID原则
- **单一职责原则(SRP)**: 每个模块只负责一个特定的功能领域
- **开闭原则(OCP)**: 对扩展开放，对修改封闭
- **里氏替换原则(LSP)**: 子类可以替换父类
- **接口隔离原则(ISP)**: 客户端不应依赖它不需要的接口
- **依赖倒置原则(DIP)**: 依赖抽象而不是具体实现

### 1.2 模块化原则
- **高内聚**: 模块内部功能紧密相关
- **低耦合**: 模块间依赖最小化
- **清晰接口**: 模块间通过明确定义的接口交互
- **可测试性**: 每个模块都可以独立测试

## 2. 目标架构概览

```
ui_handler.py (门面模式)
├── state_machine/          # 状态机管理
│   ├── __init__.py
│   ├── base_state_machine.py
│   ├── camera_state_machine.py
│   ├── calibration_state_machine.py
│   ├── detection_state_machine.py
│   └── state_machine_manager.py
├── ui_components/          # UI组件
│   ├── __init__.py
│   ├── base_renderer.py
│   ├── roi_renderer.py
│   ├── hud_renderer.py
│   ├── display_manager.py
│   └── window_manager.py
├── event_handlers/         # 事件处理
│   ├── __init__.py
│   ├── base_handler.py
│   ├── mouse_handler.py
│   ├── keyboard_handler.py
│   └── event_dispatcher.py
└── core_logic/            # 核心业务逻辑
    ├── __init__.py
    ├── base_controller.py
    ├── detection_controller.py
    ├── calibration_controller.py
    └── analysis_controller.py
```

## 3. 详细模块设计

### 3.1 state_machine 模块

#### 3.1.1 base_state_machine.py
```python
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, Optional

class StateTransition:
    """状态转换定义"""
    def __init__(self, from_state: str, to_state: str, condition: callable = None):
        self.from_state = from_state
        self.to_state = to_state
        self.condition = condition

class BaseStateMachine(ABC):
    """状态机基类"""
    
    def __init__(self, initial_state: str):
        self.current_state = initial_state
        self.transitions: Dict[str, List[StateTransition]] = {}
        self.state_handlers: Dict[str, callable] = {}
    
    @abstractmethod
    def setup_transitions(self):
        """设置状态转换规则"""
        pass
    
    @abstractmethod
    def setup_handlers(self):
        """设置状态处理器"""
        pass
    
    def add_transition(self, transition: StateTransition):
        """添加状态转换"""
        if transition.from_state not in self.transitions:
            self.transitions[transition.from_state] = []
        self.transitions[transition.from_state].append(transition)
    
    def can_transition(self, to_state: str, context: Any = None) -> bool:
        """检查是否可以转换到目标状态"""
        if self.current_state not in self.transitions:
            return False
        
        for transition in self.transitions[self.current_state]:
            if transition.to_state == to_state:
                if transition.condition is None or transition.condition(context):
                    return True
        return False
    
    def transition_to(self, new_state: str, context: Any = None) -> bool:
        """转换到新状态"""
        if self.can_transition(new_state, context):
            old_state = self.current_state
            self.current_state = new_state
            self.on_state_changed(old_state, new_state, context)
            return True
        return False
    
    def process_state(self, context: Any) -> bool:
        """处理当前状态"""
        if self.current_state in self.state_handlers:
            return self.state_handlers[self.current_state](context)
        return True
    
    def on_state_changed(self, old_state: str, new_state: str, context: Any):
        """状态改变时的回调"""
        pass
```

#### 3.1.2 camera_state_machine.py
```python
from .base_state_machine import BaseStateMachine, StateTransition
from constants import *

class CameraStateMachine(BaseStateMachine):
    """相机设置模式状态机"""
    
    def __init__(self):
        super().__init__(MODE_CAMERA_SETTINGS)
        self.setup_transitions()
        self.setup_handlers()
    
    def setup_transitions(self):
        """设置状态转换规则"""
        # 从相机设置模式可以转换到校准模式
        self.add_transition(StateTransition(
            MODE_CAMERA_SETTINGS, 
            MODE_CALIBRATION,
            condition=lambda ctx: ctx.get('key') == 13  # Enter键
        ))
    
    def setup_handlers(self):
        """设置状态处理器"""
        self.state_handlers[MODE_CAMERA_SETTINGS] = self._handle_camera_settings
    
    def _handle_camera_settings(self, app_state) -> bool:
        """处理相机设置模式"""
        # 原_run_camera_settings_mode的逻辑
        pass
```

#### 3.1.3 state_machine_manager.py
```python
from typing import Dict, Optional
from .camera_state_machine import CameraStateMachine
from .calibration_state_machine import CalibrationStateMachine
from .detection_state_machine import DetectionStateMachine

class StateMachineManager:
    """状态机管理器"""
    
    def __init__(self):
        self.state_machines: Dict[str, BaseStateMachine] = {
            MODE_CAMERA_SETTINGS: CameraStateMachine(),
            MODE_CALIBRATION: CalibrationStateMachine(),
            MODE_DETECTION: DetectionStateMachine()
        }
        self.current_mode = MODE_CAMERA_SETTINGS
    
    def get_current_state_machine(self) -> Optional[BaseStateMachine]:
        """获取当前状态机"""
        return self.state_machines.get(self.current_mode)
    
    def process_logic(self, app_state) -> bool:
        """处理当前模式的逻辑"""
        state_machine = self.get_current_state_machine()
        if state_machine:
            return state_machine.process_state(app_state)
        return False
    
    def switch_mode(self, new_mode: str, app_state) -> bool:
        """切换模式"""
        if new_mode in self.state_machines:
            old_mode = self.current_mode
            self.current_mode = new_mode
            app_state.current_mode = new_mode
            return True
        return False
```

### 3.2 ui_components 模块

#### 3.2.1 base_renderer.py
```python
from abc import ABC, abstractmethod
import cv2
import numpy as np

class BaseRenderer(ABC):
    """渲染器基类"""
    
    def __init__(self):
        self.enabled = True
        self.cache = {}
        self.last_update_time = 0
        self.update_interval = 0.1  # 默认100ms更新一次
    
    @abstractmethod
    def render(self, frame: np.ndarray, app_state) -> np.ndarray:
        """渲染到帧上"""
        pass
    
    def should_update(self, current_time: float) -> bool:
        """检查是否需要更新"""
        return (current_time - self.last_update_time) >= self.update_interval
    
    def set_enabled(self, enabled: bool):
        """设置是否启用"""
        self.enabled = enabled
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
```

#### 3.2.2 roi_renderer.py
```python
from .base_renderer import BaseRenderer
import cv2
import numpy as np

class ROIRenderer(BaseRenderer):
    """ROI渲染器"""
    
    def __init__(self):
        super().__init__()
        self.colors = {
            'led_green': (0, 255, 0),
            'led_red': (0, 0, 255),
            'digit': (255, 255, 0),
            'base_point': (0, 255, 255),
            'selected': (0, 165, 255),
            'current': (255, 0, 0)
        }
    
    def render(self, frame: np.ndarray, app_state) -> np.ndarray:
        """渲染所有ROI"""
        if not self.enabled or frame is None:
            return frame
        
        # 绘制LED ROI
        self._draw_led_rois(frame, app_state)
        
        # 绘制数码管ROI
        self._draw_digit_rois(frame, app_state)
        
        # 绘制基准点
        self._draw_base_points(frame, app_state)
        
        # 绘制当前选择的ROI
        self._draw_current_roi(frame, app_state)
        
        return frame
    
    def _draw_led_rois(self, frame: np.ndarray, app_state):
        """绘制LED ROI"""
        # 原draw_rois中LED相关的绘制逻辑
        pass
    
    def _draw_digit_rois(self, frame: np.ndarray, app_state):
        """绘制数码管ROI"""
        # 原draw_rois中数码管相关的绘制逻辑
        pass
    
    def _draw_base_points(self, frame: np.ndarray, app_state):
        """绘制基准点"""
        # 原draw_rois中基准点相关的绘制逻辑
        pass
    
    def _draw_current_roi(self, frame: np.ndarray, app_state):
        """绘制当前正在选择的ROI"""
        # 原draw_rois中当前ROI的绘制逻辑
        pass
```

#### 3.2.3 display_manager.py
```python
from typing import List
from .base_renderer import BaseRenderer
from .roi_renderer import ROIRenderer
from .hud_renderer import HUDRenderer
import cv2
import numpy as np

class DisplayManager:
    """显示管理器"""
    
    def __init__(self):
        self.renderers: List[BaseRenderer] = [
            ROIRenderer(),
            HUDRenderer()
        ]
    
    def add_renderer(self, renderer: BaseRenderer):
        """添加渲染器"""
        self.renderers.append(renderer)
    
    def remove_renderer(self, renderer_type: type):
        """移除指定类型的渲染器"""
        self.renderers = [r for r in self.renderers if not isinstance(r, renderer_type)]
    
    def prepare_frame(self, app_state) -> np.ndarray:
        """准备显示帧"""
        if app_state.display_frame is None:
            return self._create_error_frame()
        
        frame = app_state.display_frame.copy()
        
        # 依次应用所有渲染器
        for renderer in self.renderers:
            if renderer.enabled:
                frame = renderer.render(frame, app_state)
        
        return frame
    
    def _create_error_frame(self) -> np.ndarray:
        """创建错误信息帧"""
        error_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.putText(error_frame, "Error: display_frame is invalid", (50, 240),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        return error_frame
```

### 3.3 event_handlers 模块

#### 3.3.1 event_dispatcher.py
```python
from typing import Dict, List, Callable
from .mouse_handler import MouseHandler
from .keyboard_handler import KeyboardHandler

class EventDispatcher:
    """事件分发器"""
    
    def __init__(self):
        self.mouse_handler = MouseHandler()
        self.keyboard_handler = KeyboardHandler()
        self._shared_state = None
    
    def set_shared_state(self, shared_state):
        """设置共享状态"""
        self._shared_state = shared_state
        self.keyboard_handler.set_shared_state(shared_state)
    
    def setup_mouse_callback(self, window_name: str, app_state):
        """设置鼠标回调"""
        return self.mouse_handler.setup_callback(window_name, app_state)
    
    def get_key(self):
        """获取按键"""
        return self.keyboard_handler.get_key()
    
    def process_events(self, app_state):
        """处理所有事件"""
        # 处理键盘事件
        key = self.get_key()
        if key != -1 and key != 255:
            self.keyboard_handler.handle_key(key, app_state)
        
        # 鼠标事件通过回调处理，无需在此处理
```

### 3.4 core_logic 模块

#### 3.4.1 detection_controller.py
```python
from .base_controller import BaseController
import led_detector
import digit_detector
import time

class DetectionController(BaseController):
    """检测控制器"""
    
    def __init__(self):
        super().__init__()
        self.last_detection_time = 0
        self.detection_interval = 0.033  # 约30FPS
    
    def process(self, app_state) -> bool:
        """执行检测逻辑"""
        current_time = time.time()
        
        # 控制检测频率
        if current_time - self.last_detection_time < self.detection_interval:
            return True
        
        self.last_detection_time = current_time
        
        # 获取当前帧
        frame = self._get_current_frame(app_state)
        if frame is None:
            return False
        
        # LED检测
        self._detect_leds(frame, app_state)
        
        # 数码管检测
        self._detect_digits(frame, app_state)
        
        # 状态机处理
        self._process_state_machine(app_state)
        
        return True
    
    def _detect_leds(self, frame, app_state):
        """LED检测"""
        led_detector.detect_led_status(frame, app_state)
    
    def _detect_digits(self, frame, app_state):
        """数码管检测"""
        recognized_chars, segment_patterns, _ = digit_detector.detect_digit_status(frame, app_state)
        # 处理识别结果
        pass
    
    def _process_state_machine(self, app_state):
        """处理"88"分析状态机"""
        # 原_run_detection_mode中的状态机逻辑
        pass
```

## 4. 接口兼容性设计

### 4.1 门面模式实现
重构后的ui_handler.py将作为门面，保持原有接口：

```python
# ui_handler.py (重构后)
from state_machine import StateMachineManager
from ui_components import DisplayManager
from event_handlers import EventDispatcher
from core_logic import DetectionController, CalibrationController

# 全局实例
_state_machine_manager = StateMachineManager()
_display_manager = DisplayManager()
_event_dispatcher = EventDispatcher()
_detection_controller = DetectionController()
_calibration_controller = CalibrationController()

# 保持原有接口
def process_core_logic(app_state):
    """向后兼容接口"""
    return _state_machine_manager.process_logic(app_state)

def prepare_display_frame(app_state):
    """向后兼容接口"""
    return _display_manager.prepare_frame(app_state)

def setup_mouse_callback(window_name, app_state):
    """向后兼容接口"""
    return _event_dispatcher.setup_mouse_callback(window_name, app_state)

def set_shared_state(shared_state):
    """向后兼容接口"""
    return _event_dispatcher.set_shared_state(shared_state)

# 其他原有接口...
```

## 5. 数据流设计

### 5.1 数据流向
```
Input Events → EventDispatcher → StateMachineManager → CoreLogic
                                        ↓
Display ← DisplayManager ← UIComponents ← AppState
```

### 5.2 状态管理
- **AppState**: 保持为全局状态容器
- **StateMachine**: 管理模式和状态转换
- **Controllers**: 处理具体业务逻辑
- **Renderers**: 负责UI渲染

## 6. 错误处理策略

### 6.1 异常处理层次
1. **模块级**: 每个模块处理自己的异常
2. **管理器级**: 管理器处理模块间的异常
3. **门面级**: ui_handler.py处理最终的异常

### 6.2 日志策略
- 每个模块使用独立的logger
- 统一的日志格式和级别
- 性能关键路径的日志优化

## 7. 性能优化设计

### 7.1 缓存策略
- UI组件使用缓存减少重复计算
- 状态机缓存转换规则
- 检测结果缓存

### 7.2 更新频率控制
- 不同组件使用不同的更新频率
- 基于时间的更新控制
- 按需更新机制

## 8. 测试策略

### 8.1 单元测试
- 每个模块独立的单元测试
- Mock依赖项进行隔离测试
- 覆盖率要求：核心逻辑>90%，UI组件>80%

### 8.2 集成测试
- 模块间接口测试
- 端到端功能测试
- 性能基准测试

---

**总结**: 这个模块化架构设计遵循SOLID原则，通过清晰的职责分工和接口定义，既保持了向后兼容性，又提供了良好的可维护性和可扩展性。
