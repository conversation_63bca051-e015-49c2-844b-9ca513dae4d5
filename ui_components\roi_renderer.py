"""
ROI渲染器

负责绘制所有ROI相关的元素，包括：
- LED ROI（绿色和红色LED）
- 数码管ROI
- 基准点
- 当前选择的ROI
- 模板预览
"""

import cv2
import numpy as np
from typing import Any, Tuple, Optional
import logging

from .base_renderer import BaseRenderer
from constants import *


class ROIRenderer(BaseRenderer):
    """ROI渲染器"""
    
    def __init__(self):
        """初始化ROI渲染器"""
        super().__init__("ROIRenderer")
        
        # ROI颜色配置
        self.colors = {
            # LED颜色
            'led_green_on': (0, 255, 0),      # 绿色LED开启
            'led_green_off': (0, 100, 0),     # 绿色LED关闭
            'led_red_on': (0, 0, 255),        # 红色LED开启
            'led_red_off': (100, 0, 0),       # 红色LED关闭
            
            # 校准状态颜色
            'confirmed': (0, 255, 0),         # 已确认的ROI（绿色）
            'pending': (0, 165, 255),         # 待确认的ROI（橙色）
            'current': (255, 0, 0),           # 当前选择的ROI（蓝色）
            'selected': (0, 165, 255),        # 选中的ROI（橙色）
            
            # 基准点颜色
            'base_point': (0, 255, 255),      # 基准点（黄色）
            'base_center': (0, 0, 255),       # 基准点中心（红色）
            'base_template': (255, 255, 0),   # 基准点模板边框（青色）
            
            # 数码管颜色
            'digit_roi': (0, 255, 0),         # 数码管ROI（绿色）
            'digit_roi_2': (255, 255, 0),     # 第二个数码管ROI（青色）
            'segment_confirmed': (0, 255, 0), # 已确认的段（绿色）
            'segment_pending': (0, 165, 255), # 待确认的段（橙色）
            
            # 模板预览颜色
            'template_normal': (255, 255, 255), # 普通模板（白色）
            'template_fixed': (0, 255, 255),    # 固定尺寸模板（黄色）
            
            # 其他
            'corner_marker': (255, 255, 0),   # 角标记（黄色）
            'key_hint': (255, 215, 0),        # 按键提示（金黄色）
        }
        
        # 绘制参数
        self.roi_thickness = 1
        self.base_point_radius = 8
        self.base_center_radius = 2
        self.corner_size = 8
        self.dash_length = 8
        self.gap_length = 6
    
    def render(self, frame: np.ndarray, app_state: Any) -> np.ndarray:
        """
        渲染ROI到帧上
        
        Args:
            frame: 输入帧
            app_state: 应用状态
            
        Returns:
            渲染后的帧
        """
        if not self.validate_frame(frame):
            return frame
        
        if app_state.display_frame is None:
            return frame
        
        # 根据当前模式选择绘制方法
        if app_state.current_mode == MODE_CALIBRATION:
            self._render_calibration_rois(app_state)
        elif app_state.current_mode == MODE_DETECTION:
            self._render_detection_rois(app_state)
        
        return app_state.display_frame
    
    def _render_calibration_rois(self, app_state):
        """渲染校准模式的ROI"""
        state = app_state.current_calib_state
        
        if state == CALIB_STATE_BASE_POINTS_SELECT:
            self._draw_base_points(app_state)
        elif state == CALIB_STATE_LED_ROI_SELECT:
            self._draw_led_roi_selection(app_state)
        elif state == CALIB_STATE_LED_EDIT:
            self._draw_led_roi_edit(app_state)
        elif state in [CALIB_STATE_LED_SAMPLE_OFF, CALIB_STATE_LED_SAMPLE_ON]:
            self._draw_led_sampling(app_state)
        elif state in [CALIB_STATE_DIGIT_ROI_SELECT_1, CALIB_STATE_DIGIT_ROI_SELECT_2]:
            self._draw_digit_roi_selection(app_state)
        elif state == CALIB_STATE_DIGIT_SEGMENT_SELECT:
            self._draw_digit_segment_selection(app_state)
        elif state == CALIB_STATE_DIGIT_ADJUST_THRESHOLD:
            self._draw_digit_threshold_adjustment(app_state)
    
    def _render_detection_rois(self, app_state):
        """渲染检测模式的ROI"""
        # 绘制LED ROI和状态
        self._draw_led_detection_rois(app_state)
        
        # 绘制数码管ROI
        self._draw_digit_detection_rois(app_state)
        
        # 绘制基准点（如果启用对齐）
        if app_state.alignment_enabled:
            self._draw_base_points_detection(app_state)
    
    def _draw_base_points(self, app_state):
        """绘制基准点选择"""
        for i, point in enumerate(app_state.base_points):
            if point:
                x, y = point
                # 绘制基准点标记
                cv2.circle(app_state.display_frame, (x, y), 
                          self.base_point_radius, self.colors['base_point'], 2)
                cv2.circle(app_state.display_frame, (x, y), 
                          self.base_center_radius, self.colors['base_center'], -1)
                
                # 绘制基准点编号
                cv2.putText(app_state.display_frame, f"P{i+1}",
                           (x + 12, y - 8), cv2.FONT_HERSHEY_SIMPLEX, 
                           0.6, self.colors['base_point'], 2)
                
                # 绘制模板区域边框
                half_size = app_state.base_template_size // 2
                cv2.rectangle(app_state.display_frame,
                             (x - half_size, y - half_size),
                             (x + half_size, y + half_size),
                             self.colors['base_template'], 1)
    
    def _draw_led_roi_selection(self, app_state):
        """绘制LED ROI选择"""
        # 绘制已确认的ROI
        for i, roi in enumerate(app_state.led_rois):
            if roi:
                is_green = i < app_state.led_num_green
                label = f"G{i+1}" if is_green else f"R{i - app_state.led_num_green + 1}"
                
                # 确定颜色
                if i >= app_state.calib_led_roi_index:
                    color = self.colors['pending']  # 未确认的用橙色
                else:
                    color = self.colors['led_green_on'] if is_green else self.colors['led_red_on']
                
                self._draw_roi_with_label(app_state.display_frame, roi, label, color)
        
        # 绘制正在选择的ROI
        if app_state.current_rect:
            self._draw_roi(app_state.display_frame, app_state.current_rect, 
                          self.colors['current'])
        
        # 绘制模板预览
        self._draw_template_preview(app_state)
    
    def _draw_led_roi_edit(self, app_state):
        """绘制LED ROI编辑"""
        for i, roi in enumerate(app_state.led_rois):
            if roi:
                is_green = i < app_state.led_num_green
                label = f"G{i+1}" if is_green else f"R{i - app_state.led_num_green + 1}"
                
                # 根据是否选中决定颜色和样式
                if i == app_state.selected_roi_index:
                    color = self.colors['selected']
                    self._draw_roi_with_label(app_state.display_frame, roi, label, color)
                    self._draw_corner_markers(app_state.display_frame, roi)
                else:
                    color = self.colors['led_green_on'] if is_green else self.colors['led_red_on']
                    self._draw_roi_with_label(app_state.display_frame, roi, label, color)
                
                # 绘制按键提示
                self._draw_key_hint(app_state.display_frame, roi, str(i + 1))
    
    def _draw_led_sampling(self, app_state):
        """绘制LED采样状态"""
        is_on_state = app_state.current_calib_state == CALIB_STATE_LED_SAMPLE_ON
        
        for i, roi in enumerate(app_state.led_rois):
            if roi:
                is_green = i < app_state.led_num_green
                
                # 根据预期状态选择颜色
                if is_on_state:  # 期望ON
                    color = self.colors['led_green_on'] if is_green else self.colors['led_red_on']
                else:  # 期望OFF
                    color = self.colors['led_green_off'] if is_green else self.colors['led_red_off']
                
                cv2.rectangle(app_state.display_frame, 
                             (roi[0], roi[1]), (roi[0]+roi[2], roi[1]+roi[3]), 
                             color, 2)
    
    def _draw_digit_roi_selection(self, app_state):
        """绘制数码管ROI选择"""
        # 确保使用'88'图像
        if app_state.digit_calibration_image_88 is not None:
            if not np.array_equal(app_state.display_frame, app_state.digit_calibration_image_88):
                app_state.display_frame = app_state.digit_calibration_image_88.copy()
            
            # 绘制Digit 1（绿色框）
            if app_state.digit_rois[0]:
                self._draw_roi(app_state.display_frame, app_state.digit_rois[0], 
                              self.colors['digit_roi'])
            
            # 绘制Digit 2（如果已定义，用青色框）
            if (app_state.digit_rois[1] and 
                app_state.current_calib_state == CALIB_STATE_DIGIT_ROI_SELECT_2):
                self._draw_roi(app_state.display_frame, app_state.digit_rois[1], 
                              self.colors['digit_roi_2'])
            
            # 绘制正在选择的ROI（蓝色）
            if app_state.current_rect:
                self._draw_roi(app_state.display_frame, app_state.current_rect, 
                              self.colors['current'])
    
    def _draw_digit_segment_selection(self, app_state):
        """绘制数码管段选择"""
        if app_state.digit_calibration_image_88 is not None:
            if not np.array_equal(app_state.display_frame, app_state.digit_calibration_image_88):
                app_state.display_frame = app_state.digit_calibration_image_88.copy()
            
            # 绘制已确认的Digit ROIs
            for roi in app_state.digit_rois:
                if roi:
                    self._draw_roi(app_state.display_frame, roi, self.colors['digit_roi'])
            
            # 绘制当前Digit已确认的Segment ROIs
            d_idx = app_state.calib_digit_index
            if 0 <= d_idx < NUM_DIGITS:
                for s_idx, roi in enumerate(app_state.digit_segment_rois[d_idx]):
                    if roi:
                        color = (self.colors['segment_confirmed'] if s_idx < app_state.calib_segment_index 
                                else self.colors['segment_pending'])
                        self._draw_roi(app_state.display_frame, roi, color)
            
            # 绘制正在选择的Segment ROI
            if app_state.current_rect:
                self._draw_roi(app_state.display_frame, app_state.current_rect, 
                              self.colors['current'])
    
    def _draw_digit_threshold_adjustment(self, app_state):
        """绘制数码管阈值调整"""
        if app_state.digit_background_image_off is not None:
            if not np.array_equal(app_state.display_frame, app_state.digit_background_image_off):
                app_state.display_frame = app_state.digit_background_image_off.copy()
            
            try:
                temp_gray = cv2.cvtColor(app_state.display_frame, cv2.COLOR_BGR2GRAY)
                for d_idx in range(NUM_DIGITS):
                    if app_state.digit_rois[d_idx]:
                        for s_idx, roi in enumerate(app_state.digit_segment_rois[d_idx]):
                            if roi and isinstance(roi, tuple) and len(roi) == 4:
                                sx, sy, sw, sh = roi
                                if sw > 0 and sh > 0:
                                    y_end = min(sy + sh, temp_gray.shape[0])
                                    x_end = min(sx + sw, temp_gray.shape[1])
                                    roi_y = max(0, sy)
                                    roi_x = max(0, sx)
                                    if roi_y < y_end and roi_x < x_end:
                                        segment_area = temp_gray[roi_y:y_end, roi_x:x_end]
                                        if segment_area.size > 0:
                                            mean_brightness = cv2.mean(segment_area)[0]
                                            # 红色表示错误的ON，绿色表示正确的OFF
                                            color = ((0, 0, 255) if mean_brightness > app_state.digit_brightness_threshold 
                                                    else (0, 255, 0))
                                            cv2.rectangle(app_state.display_frame, 
                                                         (sx, sy), (sx + sw, sy + sh), color, 1)
            except Exception as e:
                logging.error(f"绘制阈值调整状态时出错: {e}")
    
    def _draw_led_detection_rois(self, app_state):
        """绘制检测模式的LED ROI"""
        for i, roi in enumerate(app_state.led_rois):
            if not roi or not isinstance(roi, tuple) or len(roi) != 4:
                continue
            x, y, w, h = roi
            if w <= 0 or h <= 0:
                continue
            
            is_on = app_state.led_last_status[i] if i < len(app_state.led_last_status) else False
            is_green_led = (i < app_state.led_num_green)
            led_label = f"G{i+1}" if is_green_led else f"R{i - app_state.led_num_green + 1}"
            
            # 根据LED状态和类型选择颜色
            if is_on:
                color = self.colors['led_green_on'] if is_green_led else self.colors['led_red_on']
                thickness = 2
            else:
                color = self.colors['led_green_off'] if is_green_led else self.colors['led_red_off']
                thickness = 1
            
            cv2.rectangle(app_state.display_frame, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制LED标签
            text_y = y - 5 if y > 10 else y + h + 15
            cv2.putText(app_state.display_frame, led_label, (x, text_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    def _draw_digit_detection_rois(self, app_state):
        """绘制检测模式的数码管ROI"""
        for i, roi in enumerate(app_state.digit_rois):
            if roi and isinstance(roi, tuple) and len(roi) == 4:
                x, y, w, h = roi
                if w > 0 and h > 0:
                    cv2.rectangle(app_state.display_frame, (x, y), (x + w, y + h), 
                                 self.colors['digit_roi'], 1)
                    # 绘制数码管标签
                    cv2.putText(app_state.display_frame, f"D{i+1}", (x, y - 5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, self.colors['digit_roi'], 1)
    
    def _draw_base_points_detection(self, app_state):
        """绘制检测模式的基准点"""
        for i, point in enumerate(app_state.base_points):
            if point:
                x, y = point
                cv2.circle(app_state.display_frame, (x, y), 4, self.colors['base_point'], 1)
                cv2.putText(app_state.display_frame, f"P{i+1}", (x + 8, y - 8), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, self.colors['base_point'], 1)
    
    def _draw_template_preview(self, app_state):
        """绘制模板预览"""
        if ((app_state.template_mode or app_state.fixed_template_mode) and 
            app_state.template_preview_pos):
            r = app_state.template_preview_pos
            x, y, w, h = r
            
            # 固定尺寸模板用不同颜色区分
            color = (self.colors['template_fixed'] if app_state.fixed_template_mode 
                    else self.colors['template_normal'])
            
            # 绘制虚线边框
            self._draw_dashed_rectangle(app_state.display_frame, (x, y, w, h), color)
    
    def _draw_roi(self, frame: np.ndarray, roi: Tuple[int, int, int, int], 
                  color: Tuple[int, int, int], thickness: int = 1):
        """绘制ROI矩形"""
        x, y, w, h = roi
        cv2.rectangle(frame, (x, y), (x + w, y + h), color, thickness)
    
    def _draw_roi_with_label(self, frame: np.ndarray, roi: Tuple[int, int, int, int], 
                            label: str, color: Tuple[int, int, int], thickness: int = 1):
        """绘制带标签的ROI"""
        self._draw_roi(frame, roi, color, thickness)
        x, y, w, h = roi
        text_y = y - 5 if y > 10 else y + h + 15
        cv2.putText(frame, label, (x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    def _draw_corner_markers(self, frame: np.ndarray, roi: Tuple[int, int, int, int]):
        """绘制角标记"""
        x, y, w, h = roi
        corner_color = self.colors['corner_marker']
        corner_size = self.corner_size
        
        # 四个角
        cv2.rectangle(frame, (x, y), (x + corner_size, y + corner_size), corner_color, -1)
        cv2.rectangle(frame, (x + w - corner_size, y), (x + w, y + corner_size), corner_color, -1)
        cv2.rectangle(frame, (x, y + h - corner_size), (x + corner_size, y + h), corner_color, -1)
        cv2.rectangle(frame, (x + w - corner_size, y + h - corner_size), (x + w, y + h), corner_color, -1)
    
    def _draw_key_hint(self, frame: np.ndarray, roi: Tuple[int, int, int, int], hint: str):
        """绘制按键提示"""
        x, y, w, h = roi
        hint_color = self.colors['key_hint']
        hint_y = y - 6 if y > 14 else y + h + 14
        hint_x = x + 2
        cv2.putText(frame, hint, (hint_x, hint_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, hint_color, 1)
    
    def _draw_dashed_rectangle(self, frame: np.ndarray, roi: Tuple[int, int, int, int], 
                              color: Tuple[int, int, int], thickness: int = 1):
        """绘制虚线矩形"""
        x, y, w, h = roi
        
        # 绘制顶边虚线
        for i in range(0, w, self.dash_length + self.gap_length):
            end_x = min(x + i + self.dash_length, x + w)
            cv2.line(frame, (x + i, y), (end_x, y), color, thickness)
        
        # 绘制底边虚线
        for i in range(0, w, self.dash_length + self.gap_length):
            end_x = min(x + i + self.dash_length, x + w)
            cv2.line(frame, (x + i, y + h), (end_x, y + h), color, thickness)
        
        # 绘制左边虚线
        for i in range(0, h, self.dash_length + self.gap_length):
            end_y = min(y + i + self.dash_length, y + h)
            cv2.line(frame, (x, y + i), (x, end_y), color, thickness)
        
        # 绘制右边虚线
        for i in range(0, h, self.dash_length + self.gap_length):
            end_y = min(y + i + self.dash_length, y + h)
            cv2.line(frame, (x + w, y + i), (x + w, end_y), color, thickness)
