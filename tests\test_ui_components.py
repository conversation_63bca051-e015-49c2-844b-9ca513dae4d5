"""
测试UI组件模块
"""
import pytest
import numpy as np
import cv2
import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui_components.base_renderer import BaseRenderer


class TestRenderer(BaseRenderer):
    """测试用的渲染器实现"""

    def __init__(self, name="TestRenderer"):
        super().__init__(name)
        self.render_called = False
        self.render_call_count = 0

    def render(self, frame, app_state):
        """测试渲染实现"""
        _ = app_state  # 标记参数已使用
        self.render_called = True
        self.render_call_count += 1
        # 简单地在帧上添加一个标记
        if self.validate_frame(frame):
            frame = frame.copy()
            cv2.putText(frame, "TEST", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        return frame


class TestBaseRenderer:
    """测试渲染器基类"""

    def test_renderer_creation(self):
        """测试渲染器创建"""
        renderer = TestRenderer("TestRenderer")
        assert renderer.name == "TestRenderer"
        assert renderer.enabled is True
        assert renderer.cache_enabled is True
        assert renderer.update_interval == 0.1

    def test_renderer_enable_disable(self, sample_frame, mock_app_state):
        """测试渲染器启用/禁用"""
        renderer = TestRenderer()

        # 测试启用状态
        assert renderer.enabled is True
        result = renderer.render_with_timing(sample_frame, mock_app_state)
        assert renderer.render_called is True

        # 测试禁用
        renderer.set_enabled(False)
        assert renderer.enabled is False

        # 重置调用标志
        renderer.render_called = False
        result = renderer.render_with_timing(sample_frame, mock_app_state)
        # 禁用时应该直接返回原帧，不调用render
        assert np.array_equal(result, sample_frame)

    def test_renderer_caching(self, sample_frame, mock_app_state):
        """测试渲染器缓存机制"""
        renderer = TestRenderer()
        renderer.set_update_interval(1.0)  # 设置较长的更新间隔

        # 第一次渲染
        _ = renderer.render_with_timing(sample_frame, mock_app_state)
        call_count_1 = renderer.render_call_count

        # 立即再次渲染，应该使用缓存
        _ = renderer.render_with_timing(sample_frame, mock_app_state)
        call_count_2 = renderer.render_call_count

        # 调用次数不应该增加（使用了缓存）
        assert call_count_2 == call_count_1

    def test_update_interval(self):
        """测试更新间隔控制"""
        renderer = TestRenderer()

        # 测试设置更新间隔
        renderer.set_update_interval(0.05)  # 50ms
        assert renderer.update_interval == 0.05

        # 测试最小间隔限制
        renderer.set_update_interval(0.0001)  # 0.1ms
        assert renderer.update_interval == 0.001  # 应该被限制为1ms

    def test_cache_operations(self):
        """测试缓存操作"""
        renderer = TestRenderer()

        # 测试设置和获取缓存
        renderer.set_cache("test_key", "test_value")
        assert renderer.get_cache("test_key") == "test_value"

        # 测试默认值
        assert renderer.get_cache("nonexistent_key", "default") == "default"

        # 测试清空缓存
        renderer.clear_cache()
        assert renderer.get_cache("test_key") is None

    def test_performance_stats(self):
        """测试性能统计"""
        renderer = TestRenderer()

        # 测试初始统计
        stats = renderer.get_performance_stats()
        assert stats['name'] == "TestRenderer"
        assert stats['render_count'] == 0
        assert stats['total_render_time'] == 0

        # 手动设置一些统计数据来测试功能
        renderer.render_count = 3
        renderer.total_render_time = 0.15
        renderer.last_render_time = 0.05

        stats = renderer.get_performance_stats()
        assert stats['name'] == "TestRenderer"
        assert stats['render_count'] == 3
        assert stats['total_render_time'] == 0.15
        assert abs(stats['average_render_time'] - 0.05) < 0.001

        # 测试重置统计
        renderer.reset_performance_stats()
        stats = renderer.get_performance_stats()
        assert stats['render_count'] == 0
        assert stats['total_render_time'] == 0

    def test_frame_validation(self, sample_frame):
        """测试帧验证"""
        renderer = TestRenderer()

        # 测试有效帧
        assert renderer.validate_frame(sample_frame) is True

        # 测试无效帧
        assert renderer.validate_frame(None) is False
        assert renderer.validate_frame("not_an_array") is False
        assert renderer.validate_frame(np.array([1, 2, 3])) is False  # 1D数组

        # 测试获取帧信息
        info = renderer.get_frame_info(sample_frame)
        assert info['valid'] is True
        assert info['height'] == sample_frame.shape[0]
        assert info['width'] == sample_frame.shape[1]
        assert info['channels'] == 3


class TestROIRenderer:
    """测试ROI渲染器"""

    def test_roi_renderer_creation(self):
        """测试ROI渲染器创建"""
        from ui_components.roi_renderer import ROIRenderer

        renderer = ROIRenderer()
        assert renderer.name == "ROIRenderer"
        assert renderer.enabled is True
        assert 'led_green_on' in renderer.colors
        assert 'led_red_on' in renderer.colors

    def test_led_roi_rendering(self, sample_frame, mock_app_state):
        """测试LED ROI渲染"""
        from ui_components.roi_renderer import ROIRenderer
        from constants import MODE_DETECTION

        renderer = ROIRenderer()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.led_rois = [(100, 100, 50, 50), (200, 200, 50, 50)]
        mock_app_state.led_last_status = [True, False]
        mock_app_state.led_num_green = 1

        result = renderer.render(sample_frame, mock_app_state)
        assert result is not None
        assert isinstance(result, np.ndarray)

    def test_digit_roi_rendering(self, sample_frame, mock_app_state):
        """测试数码管ROI渲染"""
        from ui_components.roi_renderer import ROIRenderer
        from constants import MODE_DETECTION

        renderer = ROIRenderer()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.digit_rois = [(150, 150, 60, 80), (250, 150, 60, 80)]

        result = renderer.render(sample_frame, mock_app_state)
        assert result is not None
        assert isinstance(result, np.ndarray)

    def test_base_point_rendering(self, sample_frame, mock_app_state):
        """测试基准点渲染"""
        from ui_components.roi_renderer import ROIRenderer
        from constants import MODE_CALIBRATION, CALIB_STATE_BASE_POINTS_SELECT

        renderer = ROIRenderer()
        mock_app_state.current_mode = MODE_CALIBRATION
        mock_app_state.current_calib_state = CALIB_STATE_BASE_POINTS_SELECT
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.base_points = [(100, 100), (200, 200), None, None]
        mock_app_state.base_template_size = 40

        result = renderer.render(sample_frame, mock_app_state)
        assert result is not None
        assert isinstance(result, np.ndarray)

    def test_current_roi_rendering(self, sample_frame, mock_app_state):
        """测试当前ROI渲染"""
        from ui_components.roi_renderer import ROIRenderer
        from constants import MODE_CALIBRATION, CALIB_STATE_LED_ROI_SELECT

        renderer = ROIRenderer()
        mock_app_state.current_mode = MODE_CALIBRATION
        mock_app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.current_rect = (120, 120, 40, 40)
        mock_app_state.led_rois = [None] * 10
        mock_app_state.calib_led_roi_index = 0
        mock_app_state.led_num_green = 5

        result = renderer.render(sample_frame, mock_app_state)
        assert result is not None
        assert isinstance(result, np.ndarray)

    def test_roi_colors(self):
        """测试ROI颜色配置"""
        from ui_components.roi_renderer import ROIRenderer

        renderer = ROIRenderer()

        # 验证关键颜色存在
        assert 'led_green_on' in renderer.colors
        assert 'led_red_on' in renderer.colors
        assert 'base_point' in renderer.colors
        assert 'current' in renderer.colors

        # 验证颜色格式正确（BGR元组）
        for color_name, color_value in renderer.colors.items():
            assert isinstance(color_value, tuple)
            assert len(color_value) == 3
            assert all(0 <= c <= 255 for c in color_value)

    def test_calibration_mode_rendering(self, sample_frame, mock_app_state):
        """测试校准模式渲染"""
        from ui_components.roi_renderer import ROIRenderer
        from constants import MODE_CALIBRATION, CALIB_STATE_LED_EDIT

        renderer = ROIRenderer()
        mock_app_state.current_mode = MODE_CALIBRATION
        mock_app_state.current_calib_state = CALIB_STATE_LED_EDIT
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.led_rois = [(100, 100, 50, 50), (200, 200, 50, 50)]
        mock_app_state.selected_roi_index = 0
        mock_app_state.led_num_green = 1

        result = renderer.render(sample_frame, mock_app_state)
        assert result is not None
        assert isinstance(result, np.ndarray)

    @pytest.mark.performance
    def test_roi_rendering_performance(self, sample_frame, mock_app_state, performance_threshold):
        """测试ROI渲染性能"""
        from ui_components.roi_renderer import ROIRenderer
        from constants import MODE_DETECTION

        renderer = ROIRenderer()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.led_rois = [(i*60, i*40, 50, 50) for i in range(10)]
        mock_app_state.led_last_status = [i % 2 == 0 for i in range(10)]
        mock_app_state.led_num_green = 5
        mock_app_state.digit_rois = [(300, 300, 60, 80), (400, 300, 60, 80)]

        import time
        start_time = time.time()
        for _ in range(100):
            renderer.render_with_timing(sample_frame, mock_app_state)
        end_time = time.time()

        avg_time = (end_time - start_time) / 100
        assert avg_time < performance_threshold['max_processing_time']


class TestHUDRenderer:
    """测试HUD渲染器"""

    def test_hud_renderer_creation(self):
        """测试HUD渲染器创建"""
        from ui_components.hud_renderer import HUDRenderer

        renderer = HUDRenderer()
        assert renderer.name == "HUDRenderer"
        assert renderer.enabled is True
        assert 'info' in renderer.colors
        assert 'fps' in renderer.colors
        assert renderer.hud_refresh_interval == 0.1

    def test_status_info_rendering(self, sample_frame, mock_app_state):
        """测试状态信息渲染"""
        from ui_components.hud_renderer import HUDRenderer
        from constants import MODE_DETECTION

        renderer = HUDRenderer()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.status_message = "Test status message"
        mock_app_state.prompt_message = "Test prompt message"
        mock_app_state.led_max_rois = 0  # 设置为0避免LED相关错误

        result = renderer.render(sample_frame, mock_app_state)
        assert result is not None
        assert isinstance(result, np.ndarray)

    def test_fps_display(self, sample_frame, mock_app_state):
        """测试FPS显示"""
        from ui_components.hud_renderer import HUDRenderer
        from constants import MODE_DETECTION

        renderer = HUDRenderer()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.fps = 30.5
        mock_app_state.window_topmost = True
        mock_app_state.led_max_rois = 0  # 设置为0避免LED相关错误

        result = renderer.render(sample_frame, mock_app_state)
        assert result is not None
        assert isinstance(result, np.ndarray)

    def test_led_status_display(self, sample_frame, mock_app_state):
        """测试LED状态显示"""
        from ui_components.hud_renderer import HUDRenderer
        from constants import MODE_DETECTION

        renderer = HUDRenderer()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.led_max_rois = 3
        mock_app_state.led_rois = [(100, 100, 50, 50), (200, 200, 50, 50), (300, 300, 50, 50)]
        mock_app_state.led_last_status = [True, False, True]
        mock_app_state.led_last_values = [(100, 150, 50), (80, 30, 20), (120, 180, 60)]
        mock_app_state.led_num_green = 2
        mock_app_state.led_gray_threshold_green = 100.0
        mock_app_state.led_green_threshold = 50.0
        mock_app_state.led_gray_threshold_red = 120.0
        mock_app_state.led_red_threshold = 60.0
        mock_app_state.led_gray_threshold_g33 = 110.0
        mock_app_state.led_green_threshold_g33 = 55.0

        result = renderer.render(sample_frame, mock_app_state)
        assert result is not None
        assert isinstance(result, np.ndarray)

    def test_digit_status_display(self, sample_frame, mock_app_state):
        """测试数码管状态显示"""
        from ui_components.hud_renderer import HUDRenderer
        from constants import MODE_DETECTION

        renderer = HUDRenderer()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.digit_rois = [(150, 150, 60, 80), (250, 150, 60, 80)]
        mock_app_state.digit_last_recognized_chars = ['8', '8']
        mock_app_state.digit_last_missing_segments = [[], []]
        mock_app_state.digit_brightness_threshold = 128.0
        mock_app_state.led_max_rois = 0  # 设置为0避免LED相关错误

        result = renderer.render(sample_frame, mock_app_state)
        assert result is not None
        assert isinstance(result, np.ndarray)

    def test_hud_caching(self, sample_frame, mock_app_state):
        """测试HUD缓存机制"""
        from ui_components.hud_renderer import HUDRenderer
        from constants import MODE_DETECTION

        renderer = HUDRenderer()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.fps = 30.0
        mock_app_state.led_max_rois = 0  # 设置为0避免LED相关错误

        # 第一次渲染，应该生成HUD
        _ = renderer.render(sample_frame, mock_app_state)
        assert renderer.hud_canvas is not None
        assert renderer.hud_mask is not None

        # 立即再次渲染，应该使用缓存
        result2 = renderer.render(sample_frame, mock_app_state)
        assert result2 is not None

    def test_font_scaling(self):
        """测试字体缩放"""
        from ui_components.hud_renderer import HUDRenderer

        renderer = HUDRenderer()

        # 测试不同分辨率的字体缩放
        font_scale_low, led_font_scale_low = renderer._calculate_font_scales(800)
        font_scale_normal, led_font_scale_normal = renderer._calculate_font_scales(1200)
        font_scale_high, led_font_scale_high = renderer._calculate_font_scales(1920)

        # 高分辨率应该有更大的字体
        assert font_scale_high >= font_scale_normal >= font_scale_low
        assert led_font_scale_high >= led_font_scale_normal >= led_font_scale_low

    def test_hud_cache_clearing(self, sample_frame, mock_app_state):
        """测试HUD缓存清理"""
        from ui_components.hud_renderer import HUDRenderer
        from constants import MODE_DETECTION

        renderer = HUDRenderer()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.led_max_rois = 0  # 设置为0避免LED相关错误

        # 生成HUD缓存
        renderer.render(sample_frame, mock_app_state)
        assert renderer.hud_canvas is not None

        # 清理缓存
        renderer.clear_hud_cache(mock_app_state)
        assert renderer.hud_canvas is None
        assert renderer.hud_mask is None

    @pytest.mark.performance
    def test_hud_rendering_performance(self, sample_frame, mock_app_state, performance_threshold):
        """测试HUD渲染性能"""
        from ui_components.hud_renderer import HUDRenderer
        from constants import MODE_DETECTION

        renderer = HUDRenderer()
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.fps = 30.0
        mock_app_state.led_max_rois = 10
        mock_app_state.led_rois = [(i*60, i*40, 50, 50) for i in range(10)]
        mock_app_state.led_last_status = [i % 2 == 0 for i in range(10)]
        mock_app_state.led_last_values = [(100+i*10, 150+i*5, 50+i*3) for i in range(10)]
        mock_app_state.led_num_green = 5

        import time
        start_time = time.time()
        for _ in range(50):  # 减少测试次数，因为HUD渲染相对较重
            renderer.render_with_timing(sample_frame, mock_app_state)
        end_time = time.time()

        avg_time = (end_time - start_time) / 50
        assert avg_time < performance_threshold['max_processing_time']


class TestDisplayManager:
    """测试显示管理器"""

    def test_display_manager_creation(self):
        """测试显示管理器创建"""
        from ui_components.display_manager import DisplayManager

        manager = DisplayManager()
        assert len(manager.renderers) == 0
        assert len(manager.renderer_map) == 0
        assert manager.render_count == 0

    def test_renderer_management(self):
        """测试渲染器管理"""
        from ui_components.display_manager import DisplayManager

        manager = DisplayManager()
        renderer = TestRenderer("TestRenderer1")

        # 测试添加渲染器
        assert manager.add_renderer(renderer) is True
        assert len(manager.renderers) == 1
        assert "TestRenderer1" in manager.renderer_map

        # 测试获取渲染器
        retrieved = manager.get_renderer("TestRenderer1")
        assert retrieved is renderer

        # 测试列出渲染器
        names = manager.list_renderers()
        assert "TestRenderer1" in names

        # 测试移除渲染器
        assert manager.remove_renderer("TestRenderer1") is True
        assert len(manager.renderers) == 0
        assert "TestRenderer1" not in manager.renderer_map

    def test_frame_preparation(self, sample_frame, mock_app_state):
        """测试帧准备"""
        from ui_components.display_manager import DisplayManager

        manager = DisplayManager()

        # 测试正常帧准备
        prepared_frame = manager.prepare_frame(sample_frame, mock_app_state)
        assert prepared_frame is not None
        assert isinstance(prepared_frame, np.ndarray)
        assert prepared_frame.shape == sample_frame.shape

        # 测试None帧处理
        error_frame = manager.prepare_frame(None, mock_app_state)
        assert error_frame is not None
        assert isinstance(error_frame, np.ndarray)

    def test_renderer_composition(self, sample_frame, mock_app_state):
        """测试渲染器组合"""
        from ui_components.display_manager import DisplayManager

        manager = DisplayManager()

        # 添加多个渲染器
        renderer1 = TestRenderer("Renderer1")
        renderer2 = TestRenderer("Renderer2")

        manager.add_renderer(renderer1)
        manager.add_renderer(renderer2)

        # 测试渲染
        result = manager.render_all(sample_frame, mock_app_state)
        assert result is not None
        assert isinstance(result, np.ndarray)

        # 验证两个渲染器都被调用
        assert renderer1.render_called is True
        assert renderer2.render_called is True

    def test_specific_renderer_rendering(self, sample_frame, mock_app_state):
        """测试指定渲染器渲染"""
        from ui_components.display_manager import DisplayManager

        manager = DisplayManager()

        # 添加多个渲染器
        renderer1 = TestRenderer("Renderer1")
        renderer2 = TestRenderer("Renderer2")

        manager.add_renderer(renderer1)
        manager.add_renderer(renderer2)

        # 只使用第一个渲染器
        result = manager.render_with_specific_renderers(
            sample_frame, mock_app_state, ["Renderer1"]
        )
        assert result is not None
        assert isinstance(result, np.ndarray)

        # 验证只有第一个渲染器被调用
        assert renderer1.render_called is True
        assert renderer2.render_called is False

    def test_renderer_enable_disable(self, sample_frame, mock_app_state):
        """测试渲染器启用/禁用"""
        from ui_components.display_manager import DisplayManager

        manager = DisplayManager()
        renderer = TestRenderer("TestRenderer")

        manager.add_renderer(renderer)

        # 测试禁用渲染器
        assert manager.enable_renderer("TestRenderer", False) is True
        assert renderer.enabled is False

        # 渲染时不应该调用禁用的渲染器
        manager.render_all(sample_frame, mock_app_state)
        assert renderer.render_called is False

        # 测试启用渲染器
        assert manager.enable_renderer("TestRenderer", True) is True
        assert renderer.enabled is True

        # 渲染时应该调用启用的渲染器
        manager.render_all(sample_frame, mock_app_state)
        assert renderer.render_called is True

    def test_error_frame_creation(self):
        """测试错误帧创建"""
        from ui_components.display_manager import DisplayManager

        manager = DisplayManager()

        # 测试创建错误帧
        error_frame = manager._create_error_frame("Test error message")
        assert error_frame is not None
        assert isinstance(error_frame, np.ndarray)
        assert len(error_frame.shape) == 3
        assert error_frame.shape[2] == 3

    def test_performance_stats(self):
        """测试性能统计"""
        from ui_components.display_manager import DisplayManager

        manager = DisplayManager()
        renderer = TestRenderer("TestRenderer")
        manager.add_renderer(renderer)

        # 测试初始统计
        stats = manager.get_performance_stats()
        assert stats['render_count'] == 0
        assert stats['total_render_time'] == 0
        assert stats['renderer_count'] == 1
        assert stats['enabled_renderer_count'] == 1

        # 手动设置一些统计数据来测试功能
        manager.render_count = 3
        manager.total_render_time = 0.15
        manager.last_render_time = 0.05

        stats = manager.get_performance_stats()
        assert stats['render_count'] == 3
        assert stats['total_render_time'] == 0.15
        assert abs(stats['average_render_time'] - 0.05) < 0.001

        # 测试重置统计
        manager.reset_performance_stats()
        stats = manager.get_performance_stats()
        assert stats['render_count'] == 0
        assert stats['total_render_time'] == 0

    def test_frame_copy_and_validation(self):
        """测试帧复制和验证"""
        from ui_components.display_manager import DisplayManager

        manager = DisplayManager()

        # 测试帧复制设置
        manager.set_frame_copy_enabled(False)
        assert manager.frame_copy_enabled is False

        manager.set_frame_copy_enabled(True)
        assert manager.frame_copy_enabled is True

        # 测试帧验证设置
        manager.set_frame_validation_enabled(False)
        assert manager.frame_validation_enabled is False

        manager.set_frame_validation_enabled(True)
        assert manager.frame_validation_enabled is True

    @pytest.mark.performance
    def test_display_manager_performance(self, sample_frame, mock_app_state, performance_threshold):
        """测试显示管理器性能"""
        from ui_components.display_manager import DisplayManager

        manager = DisplayManager()

        # 添加多个渲染器
        for i in range(3):
            renderer = TestRenderer(f"Renderer{i}")
            manager.add_renderer(renderer)

        import time
        start_time = time.time()
        for _ in range(50):
            manager.render_all(sample_frame, mock_app_state)
        end_time = time.time()

        avg_time = (end_time - start_time) / 50
        assert avg_time < performance_threshold['max_processing_time']


class TestWindowManager:
    """测试窗口管理器"""
    
    def test_window_manager_creation(self):
        """测试窗口管理器创建"""
        # TODO: 重构后实现
        pass
    
    def test_window_topmost_toggle(self):
        """测试窗口置顶切换"""
        # TODO: 重构后实现
        # 验证窗口置顶功能
        pass
    
    def test_window_properties(self):
        """测试窗口属性管理"""
        # TODO: 重构后实现
        # 验证窗口大小、位置等属性管理
        pass


class TestUIComponentsIntegration:
    """UI组件集成测试"""

    @pytest.mark.integration
    def test_full_ui_rendering(self, sample_frame, mock_app_state):
        """测试完整UI渲染"""
        from ui_components.display_manager import DisplayManager
        from ui_components.roi_renderer import ROIRenderer
        from ui_components.hud_renderer import HUDRenderer
        from constants import MODE_DETECTION

        # 创建显示管理器和渲染器
        display_manager = DisplayManager()
        roi_renderer = ROIRenderer()
        hud_renderer = HUDRenderer()

        # 添加渲染器到管理器
        display_manager.add_renderer(roi_renderer)
        display_manager.add_renderer(hud_renderer)

        # 设置测试状态
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.fps = 30.0
        mock_app_state.led_max_rois = 2
        mock_app_state.led_rois = [(100, 100, 50, 50), (200, 200, 50, 50)]
        mock_app_state.led_last_status = [True, False]
        mock_app_state.led_last_values = [(100, 150, 50), (80, 30, 20)]
        mock_app_state.led_num_green = 1

        # 执行完整渲染
        result = display_manager.render_all(sample_frame, mock_app_state)

        # 验证结果
        assert result is not None
        assert isinstance(result, np.ndarray)
        assert result.shape == sample_frame.shape

        # 验证渲染器都被调用
        stats = display_manager.get_performance_stats()
        assert stats['render_count'] == 1
        assert stats['renderer_count'] == 2
        assert stats['enabled_renderer_count'] == 2

    @pytest.mark.integration
    def test_ui_state_consistency(self):
        """测试UI状态一致性"""
        from ui_components.display_manager import DisplayManager
        from ui_components.roi_renderer import ROIRenderer
        from ui_components.hud_renderer import HUDRenderer

        # 创建组件
        display_manager = DisplayManager()
        roi_renderer = ROIRenderer()
        hud_renderer = HUDRenderer()

        # 添加渲染器
        display_manager.add_renderer(roi_renderer)
        display_manager.add_renderer(hud_renderer)

        # 验证初始状态
        assert len(display_manager.list_renderers()) == 2
        assert "ROIRenderer" in display_manager.list_renderers()
        assert "HUDRenderer" in display_manager.list_renderers()

        # 测试启用/禁用状态一致性
        display_manager.enable_renderer("ROIRenderer", False)
        roi_renderer_ref = display_manager.get_renderer("ROIRenderer")
        assert roi_renderer_ref.enabled is False

        display_manager.enable_renderer("ROIRenderer", True)
        assert roi_renderer_ref.enabled is True

        # 测试性能统计一致性
        stats = display_manager.get_performance_stats()
        assert stats['renderer_count'] == 2
        assert stats['enabled_renderer_count'] == 2

    @pytest.mark.integration
    @pytest.mark.performance
    def test_ui_performance_integration(self, sample_frame, mock_app_state, performance_threshold):
        """测试UI性能集成"""
        from ui_components.display_manager import DisplayManager
        from ui_components.roi_renderer import ROIRenderer
        from ui_components.hud_renderer import HUDRenderer
        from constants import MODE_DETECTION

        # 创建完整的UI渲染管道
        display_manager = DisplayManager()
        roi_renderer = ROIRenderer()
        hud_renderer = HUDRenderer()

        # 禁用缓存以确保每次都渲染
        roi_renderer.cache_enabled = False
        hud_renderer.cache_enabled = False
        roi_renderer.set_update_interval(0.001)
        hud_renderer.set_update_interval(0.001)

        display_manager.add_renderer(roi_renderer)
        display_manager.add_renderer(hud_renderer)

        # 设置复杂的测试场景
        mock_app_state.current_mode = MODE_DETECTION
        mock_app_state.display_frame = sample_frame.copy()
        mock_app_state.fps = 30.0
        mock_app_state.led_max_rois = 10
        mock_app_state.led_rois = [(i*60, i*40, 50, 50) for i in range(10)]
        mock_app_state.led_last_status = [i % 2 == 0 for i in range(10)]
        mock_app_state.led_last_values = [(100+i*10, 150+i*5, 50+i*3) for i in range(10)]
        mock_app_state.led_num_green = 5
        mock_app_state.digit_rois = [(300, 300, 60, 80), (400, 300, 60, 80)]
        mock_app_state.digit_last_recognized_chars = ['8', '8']
        mock_app_state.digit_last_missing_segments = [[], []]
        mock_app_state.digit_brightness_threshold = 128.0

        # 性能测试
        import time
        start_time = time.time()
        for _ in range(30):  # 测试30帧
            # 强制更新每个渲染器
            roi_renderer.force_update_next()
            hud_renderer.force_update_next()
            display_manager.render_all(sample_frame, mock_app_state)
        end_time = time.time()

        avg_time = (end_time - start_time) / 30

        # 验证性能满足要求
        assert avg_time < performance_threshold['max_processing_time']

        # 验证统计信息
        stats = display_manager.get_performance_stats()
        assert stats['render_count'] == 30
        assert stats['total_render_time'] > 0
        assert stats['average_render_time'] > 0

        # 验证各个渲染器的性能
        for renderer_name, renderer_stats in stats['renderer_stats'].items():
            assert renderer_stats['render_count'] == 30
            assert renderer_stats['total_render_time'] > 0
