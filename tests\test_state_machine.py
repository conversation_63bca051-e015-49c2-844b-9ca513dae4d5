"""
测试状态机模块
"""
import pytest
import sys
import os
import time
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from state_machine.base_state_machine import BaseStateMachine, StateTransition


class TestStateMachine(BaseStateMachine):
    """测试用的状态机实现"""

    def __init__(self):
        super().__init__("INITIAL", "TestStateMachine")

    def setup_transitions(self):
        """设置测试用的状态转换"""
        # INITIAL -> PROCESSING
        self.add_transition(StateTransition(
            "INITIAL", "PROCESSING",
            condition=lambda ctx: ctx.get('can_process', True) if ctx else True
        ))

        # PROCESSING -> COMPLETE
        self.add_transition(StateTransition(
            "PROCESSING", "COMPLETE",
            condition=lambda ctx: ctx.get('is_done', False) if ctx else False
        ))

        # COMPLETE -> INITIAL (reset)
        self.add_transition(StateTransition("COMPLETE", "INITIAL"))

    def setup_handlers(self):
        """设置测试用的状态处理器"""
        self.add_state_handler("INITIAL", self._handle_initial)
        self.add_state_handler("PROCESSING", self._handle_processing)
        self.add_state_handler("COMPLETE", self._handle_complete)

    def _handle_initial(self, context):
        """处理初始状态"""
        _ = context  # 标记参数已使用
        return True

    def _handle_processing(self, context):
        """处理处理状态"""
        _ = context  # 标记参数已使用
        return True

    def _handle_complete(self, context):
        """处理完成状态"""
        _ = context  # 标记参数已使用
        return True


class TestBaseStateMachine:
    """测试状态机基类"""

    def test_state_machine_creation(self):
        """测试状态机创建"""
        sm = TestStateMachine()
        assert sm.get_current_state() == "INITIAL"
        assert sm.get_previous_state() is None
        assert sm.name == "TestStateMachine"

    def test_state_transition(self):
        """测试状态转换"""
        sm = TestStateMachine()

        # 测试有效转换
        assert sm.can_transition_to("PROCESSING")
        assert sm.transition_to("PROCESSING")
        assert sm.get_current_state() == "PROCESSING"
        assert sm.get_previous_state() == "INITIAL"

    def test_transition_conditions(self):
        """测试转换条件"""
        sm = TestStateMachine()

        # 转换到PROCESSING（无条件）
        sm.transition_to("PROCESSING")

        # 测试条件转换
        context = {'is_done': False}
        assert not sm.can_transition_to("COMPLETE", context)

        context['is_done'] = True
        assert sm.can_transition_to("COMPLETE", context)
        assert sm.transition_to("COMPLETE", context)
        assert sm.get_current_state() == "COMPLETE"

    def test_state_processing(self):
        """测试状态处理"""
        sm = TestStateMachine()

        # 测试状态处理
        assert sm.process_state() is True

        # 切换状态后继续测试
        sm.transition_to("PROCESSING")
        assert sm.process_state() is True

    def test_state_history(self):
        """测试状态历史"""
        sm = TestStateMachine()

        history = sm.get_state_history()
        assert len(history) == 1
        assert history[0][0] == "INITIAL"

        sm.transition_to("PROCESSING")
        history = sm.get_state_history()
        assert len(history) == 2
        assert history[1][0] == "PROCESSING"

    def test_available_transitions(self):
        """测试可用转换"""
        sm = TestStateMachine()

        transitions = sm.get_available_transitions()
        assert "PROCESSING" in transitions

        sm.transition_to("PROCESSING")
        transitions = sm.get_available_transitions()
        assert "COMPLETE" in transitions

    def test_state_duration(self):
        """测试状态持续时间"""
        sm = TestStateMachine()

        time.sleep(0.01)  # 等待一小段时间
        duration = sm.get_state_duration()
        assert duration > 0

    def test_reset_to_initial(self):
        """测试重置到初始状态"""
        sm = TestStateMachine()

        # 切换几个状态
        sm.transition_to("PROCESSING")
        sm.transition_to("COMPLETE", {'is_done': True})

        # 重置
        sm.reset_to_initial()
        assert sm.get_current_state() == "INITIAL"
        assert sm.get_previous_state() is None


class TestCameraStateMachine:
    """测试相机设置状态机"""

    def test_camera_state_initialization(self):
        """测试相机状态机初始化"""
        from state_machine.camera_state_machine import CameraStateMachine

        sm = CameraStateMachine()
        assert sm.get_current_state() == CameraStateMachine.STATE_CAMERA_INIT
        assert sm.name == "CameraStateMachine"

    def test_camera_available_transition(self, mock_app_state, mock_camera):
        """测试相机可用时的状态转换"""
        from state_machine.camera_state_machine import CameraStateMachine

        sm = CameraStateMachine()
        mock_app_state.cap = mock_camera

        # 处理初始状态，应该转换到设置状态
        sm.process_state(mock_app_state)
        assert sm.get_current_state() == CameraStateMachine.STATE_CAMERA_SETTINGS

    def test_camera_unavailable_transition(self, mock_app_state):
        """测试相机不可用时的状态转换"""
        from state_machine.camera_state_machine import CameraStateMachine

        sm = CameraStateMachine()
        mock_app_state.cap = None

        # 处理初始状态，应该转换到错误状态
        sm.process_state(mock_app_state)
        assert sm.get_current_state() == CameraStateMachine.STATE_CAMERA_ERROR

    def test_camera_settings_display(self, mock_app_state, mock_camera, sample_frame):
        """测试相机设置显示"""
        from state_machine.camera_state_machine import CameraStateMachine

        sm = CameraStateMachine()
        mock_app_state.cap = mock_camera
        mock_camera.read.return_value = (True, sample_frame)

        # 转换到设置状态
        sm.transition_to(CameraStateMachine.STATE_CAMERA_SETTINGS, mock_app_state)

        # 处理设置状态
        sm.process_state(mock_app_state)

        # 验证显示帧已设置
        assert mock_app_state.display_frame is not None
        # 验证状态信息已设置
        assert "Resolution:" in mock_app_state.status_message
        assert "Exposure:" in mock_app_state.status_message
        assert "Brightness:" in mock_app_state.status_message


class TestCalibrationStateMachine:
    """测试校准状态机"""

    def test_calibration_state_initialization(self):
        """测试校准状态机初始化"""
        from state_machine.calibration_state_machine import CalibrationStateMachine
        from constants import CALIB_STATE_START

        sm = CalibrationStateMachine()
        assert sm.get_current_state() == CALIB_STATE_START
        assert sm.name == "CalibrationStateMachine"

    def test_calibration_start_state(self, mock_app_state, mock_camera, sample_frame):
        """测试校准开始状态"""
        from state_machine.calibration_state_machine import CalibrationStateMachine

        sm = CalibrationStateMachine()
        mock_app_state.cap = mock_camera
        mock_camera.read.return_value = (True, sample_frame)

        # 处理开始状态
        result = sm.process_state(mock_app_state)
        assert result is True
        assert "Calibration:" in mock_app_state.prompt_message

    def test_base_point_transition(self, mock_app_state, mock_camera, sample_frame):
        """测试基准点状态转换"""
        from state_machine.calibration_state_machine import CalibrationStateMachine
        from constants import CALIB_STATE_BASE_POINTS_SELECT

        sm = CalibrationStateMachine()
        mock_app_state.cap = mock_camera
        mock_camera.read.return_value = (True, sample_frame)

        # 模拟按下'b'键
        sm.last_key = ord('b')

        # 检查是否可以转换到基准点选择状态
        assert sm.can_transition_to(CALIB_STATE_BASE_POINTS_SELECT, mock_app_state)

        # 执行转换
        assert sm.transition_to(CALIB_STATE_BASE_POINTS_SELECT, mock_app_state)
        assert sm.get_current_state() == CALIB_STATE_BASE_POINTS_SELECT

    def test_led_calibration_transition(self, mock_app_state, mock_camera, sample_frame):
        """测试LED校准状态转换"""
        from state_machine.calibration_state_machine import CalibrationStateMachine
        from constants import CALIB_STATE_LED_ROI_SELECT

        sm = CalibrationStateMachine()
        mock_app_state.cap = mock_camera
        mock_camera.read.return_value = (True, sample_frame)

        # 模拟按下'l'键
        sm.last_key = ord('l')

        # 检查是否可以转换到LED ROI选择状态
        assert sm.can_transition_to(CALIB_STATE_LED_ROI_SELECT, mock_app_state)

        # 执行转换
        assert sm.transition_to(CALIB_STATE_LED_ROI_SELECT, mock_app_state)
        assert sm.get_current_state() == CALIB_STATE_LED_ROI_SELECT


class TestDetectionStateMachine:
    """测试检测状态机"""

    def test_detection_state_initialization(self):
        """测试检测状态机初始化"""
        from state_machine.detection_state_machine import DetectionStateMachine

        sm = DetectionStateMachine()
        assert sm.get_current_state() == DetectionStateMachine.STATE_DETECTION_RUNNING
        assert sm.name == "DetectionStateMachine"
        assert sm.analysis_state == DetectionStateMachine.ANALYSIS_IDLE

    def test_detection_running_state(self, mock_app_state, mock_camera, sample_frame):
        """测试检测运行状态"""
        from state_machine.detection_state_machine import DetectionStateMachine

        sm = DetectionStateMachine()
        mock_app_state.cap = mock_camera
        mock_camera.read.return_value = (True, sample_frame)

        # 处理检测运行状态
        result = sm.process_state(mock_app_state)
        assert result is True
        assert mock_app_state.display_frame is not None
        assert mock_app_state.current_frame is not None

    def test_camera_error_transition(self, mock_app_state):
        """测试相机错误状态转换"""
        from state_machine.detection_state_machine import DetectionStateMachine

        sm = DetectionStateMachine()
        mock_app_state.cap = None  # 模拟相机不可用

        # 处理状态，应该转换到错误状态
        sm.process_state(mock_app_state)
        assert sm.get_current_state() == DetectionStateMachine.STATE_DETECTION_ERROR

    def test_88_trigger_analysis(self, mock_app_state, mock_camera, sample_frame):
        """测试"88"触发的分析状态机"""
        from state_machine.detection_state_machine import DetectionStateMachine

        sm = DetectionStateMachine()
        mock_app_state.cap = mock_camera
        mock_camera.read.return_value = (True, sample_frame)
        mock_app_state.log_file_handler = Mock()

        # 模拟检测到"88"
        sm.last_digit_display = "88"

        # 处理分析状态机
        sm._process_analysis_state_machine(mock_app_state)

        # 应该转换到日志记录状态
        assert sm.analysis_state == DetectionStateMachine.ANALYSIS_LOGGING

    def test_1p_trigger_signal(self, mock_app_state, mock_camera, sample_frame):
        """测试"1P"触发的信号发送"""
        from state_machine.detection_state_machine import DetectionStateMachine

        sm = DetectionStateMachine()
        mock_app_state.cap = mock_camera
        mock_camera.read.return_value = (True, sample_frame)

        # 模拟检测到"1P"
        sm.last_digit_display = "1P"

        # 模拟async_task_manager
        with patch('async_task_manager.get_task_manager') as mock_task_manager:
            mock_manager = Mock()
            mock_task_manager.return_value = mock_manager

            # 处理分析状态机
            sm._process_analysis_state_machine(mock_app_state)

            # 应该提交CPU信号任务
            mock_manager.submit_task.assert_called_once()


class TestStateMachineManager:
    """测试状态机管理器"""

    def test_manager_initialization(self):
        """测试管理器初始化"""
        from state_machine.state_machine_manager import StateMachineManager
        from constants import MODE_CAMERA_SETTINGS, MODE_CALIBRATION, MODE_DETECTION

        manager = StateMachineManager()

        # 验证初始状态
        assert manager.get_current_mode() == MODE_CAMERA_SETTINGS

        # 验证所有状态机都已注册
        available_modes = manager.get_available_modes()
        assert MODE_CAMERA_SETTINGS in available_modes
        assert MODE_CALIBRATION in available_modes
        assert MODE_DETECTION in available_modes

    def test_mode_switching(self, mock_app_state):
        """测试模式切换"""
        from state_machine.state_machine_manager import StateMachineManager
        from constants import MODE_CALIBRATION, MODE_DETECTION

        manager = StateMachineManager()

        # 测试切换到校准模式
        assert manager.switch_mode(MODE_CALIBRATION, mock_app_state)
        assert manager.get_current_mode() == MODE_CALIBRATION
        assert mock_app_state.current_mode == MODE_CALIBRATION

        # 测试切换到检测模式
        assert manager.switch_mode(MODE_DETECTION, mock_app_state)
        assert manager.get_current_mode() == MODE_DETECTION
        assert mock_app_state.current_mode == MODE_DETECTION

    def test_process_logic_delegation(self, mock_app_state, mock_camera, sample_frame):
        """测试逻辑处理委托"""
        from state_machine.state_machine_manager import StateMachineManager

        manager = StateMachineManager()
        mock_app_state.cap = mock_camera
        mock_camera.read.return_value = (True, sample_frame)

        # 测试处理逻辑
        result = manager.process_logic(mock_app_state)
        assert result is True

        # 验证当前状态机被调用
        current_sm = manager.get_current_state_machine()
        assert current_sm is not None

    def test_state_machine_retrieval(self):
        """测试状态机获取"""
        from state_machine.state_machine_manager import StateMachineManager
        from constants import MODE_CAMERA_SETTINGS, MODE_CALIBRATION, MODE_DETECTION

        manager = StateMachineManager()

        # 测试获取特定模式的状态机
        camera_sm = manager.get_state_machine(MODE_CAMERA_SETTINGS)
        assert camera_sm is not None
        assert camera_sm.name == "CameraStateMachine"

        calibration_sm = manager.get_state_machine(MODE_CALIBRATION)
        assert calibration_sm is not None
        assert calibration_sm.name == "CalibrationStateMachine"

        detection_sm = manager.get_state_machine(MODE_DETECTION)
        assert detection_sm is not None
        assert detection_sm.name == "DetectionStateMachine"

    def test_status_info(self):
        """测试状态信息获取"""
        from state_machine.state_machine_manager import StateMachineManager

        manager = StateMachineManager()
        status_info = manager.get_status_info()

        assert 'current_mode' in status_info
        assert 'available_modes' in status_info
        assert 'current_state' in status_info
        assert len(status_info['available_modes']) == 3


class TestStateMachineIntegration:
    """状态机集成测试"""
    
    @pytest.mark.integration
    def test_full_workflow(self):
        """测试完整工作流程"""
        # TODO: 重构后实现
        pass
    
    @pytest.mark.integration
    def test_state_persistence(self):
        """测试状态持久化"""
        # TODO: 重构后实现
        pass
