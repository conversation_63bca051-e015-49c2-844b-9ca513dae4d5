"""
校准模式状态机

负责处理校准模式的复杂状态转换和逻辑处理，包括：
- 基准点校准
- LED ROI校准
- 数码管校准
- 各种校准子状态的管理
"""

import cv2
import numpy as np
import time
import logging

from .base_state_machine import BaseStateMachine, StateTransition
from constants import *
import config_manager
import led_detector
import digit_detector
from roi_fine_tune import handle_roi_fine_tune


class CalibrationStateMachine(BaseStateMachine):
    """校准模式状态机"""
    
    def __init__(self):
        """初始化校准状态机"""
        # 使用constants中定义的校准状态作为状态机状态
        super().__init__(CALIB_STATE_START, "CalibrationStateMachine")
        
        # 校准相关的临时状态
        self.frame_read_failed = False
        self.last_key = -1
    
    def setup_transitions(self):
        """设置状态转换规则"""
        # 从开始状态可以转换到各种校准子状态
        self._setup_start_transitions()
        
        # 基准点校准转换
        self._setup_base_point_transitions()
        
        # LED校准转换
        self._setup_led_calibration_transitions()
        
        # 数码管校准转换
        self._setup_digit_calibration_transitions()
        
        # 通用转换（返回开始状态、退出等）
        self._setup_common_transitions()
    
    def _setup_start_transitions(self):
        """设置开始状态的转换"""
        # 开始 -> 基准点选择
        self.add_transition(StateTransition(
            CALIB_STATE_START, CALIB_STATE_BASE_POINTS_SELECT,
            condition=lambda ctx: self._key_pressed(ord('b'))
        ))
        
        # 开始 -> LED ROI选择
        self.add_transition(StateTransition(
            CALIB_STATE_START, CALIB_STATE_LED_ROI_SELECT,
            condition=lambda ctx: self._key_pressed(ord('l'))
        ))
        
        # 开始 -> 数码管捕获88
        self.add_transition(StateTransition(
            CALIB_STATE_START, CALIB_STATE_DIGIT_CAPTURE_88,
            condition=lambda ctx: self._key_pressed(ord('d'))
        ))
        
        # 开始 -> LED编辑模式
        self.add_transition(StateTransition(
            CALIB_STATE_START, CALIB_STATE_LED_EDIT,
            condition=lambda ctx: self._key_pressed(ord('e')) and self._has_led_rois(ctx)
        ))
    
    def _setup_base_point_transitions(self):
        """设置基准点校准转换"""
        # 基准点选择 -> 开始（完成或取消）
        self.add_transition(StateTransition(
            CALIB_STATE_BASE_POINTS_SELECT, CALIB_STATE_START,
            condition=lambda ctx: self._key_pressed(27) or self._base_points_complete(ctx)  # Esc或完成
        ))
    
    def _setup_led_calibration_transitions(self):
        """设置LED校准转换"""
        # LED ROI选择 -> LED采样OFF
        self.add_transition(StateTransition(
            CALIB_STATE_LED_ROI_SELECT, CALIB_STATE_LED_SAMPLE_OFF,
            condition=lambda ctx: self._led_rois_defined(ctx)
        ))
        
        # LED采样OFF -> LED采样ON
        self.add_transition(StateTransition(
            CALIB_STATE_LED_SAMPLE_OFF, CALIB_STATE_LED_SAMPLE_ON,
            condition=lambda ctx: self._sampling_complete(ctx, 'off')
        ))
        
        # LED采样ON -> LED分析
        self.add_transition(StateTransition(
            CALIB_STATE_LED_SAMPLE_ON, CALIB_STATE_LED_ANALYZE,
            condition=lambda ctx: self._sampling_complete(ctx, 'on')
        ))
        
        # LED分析 -> 开始（完成）
        self.add_transition(StateTransition(
            CALIB_STATE_LED_ANALYZE, CALIB_STATE_START,
            condition=lambda ctx: self._analysis_complete(ctx)
        ))
        
        # LED编辑 -> 开始
        self.add_transition(StateTransition(
            CALIB_STATE_LED_EDIT, CALIB_STATE_START,
            condition=lambda ctx: self._key_pressed(27)  # Esc
        ))
    
    def _setup_digit_calibration_transitions(self):
        """设置数码管校准转换"""
        # 数码管捕获88 -> ROI选择1
        self.add_transition(StateTransition(
            CALIB_STATE_DIGIT_CAPTURE_88, CALIB_STATE_DIGIT_ROI_SELECT_1,
            condition=lambda ctx: self._digit_88_captured(ctx)
        ))
        
        # ROI选择1 -> ROI选择2
        self.add_transition(StateTransition(
            CALIB_STATE_DIGIT_ROI_SELECT_1, CALIB_STATE_DIGIT_ROI_SELECT_2,
            condition=lambda ctx: self._digit_roi_1_defined(ctx)
        ))
        
        # ROI选择2 -> 段选择
        self.add_transition(StateTransition(
            CALIB_STATE_DIGIT_ROI_SELECT_2, CALIB_STATE_DIGIT_SEGMENT_SELECT,
            condition=lambda ctx: self._digit_roi_2_defined(ctx)
        ))
        
        # 段选择 -> 背景捕获
        self.add_transition(StateTransition(
            CALIB_STATE_DIGIT_SEGMENT_SELECT, CALIB_STATE_DIGIT_CAPTURE_BG,
            condition=lambda ctx: self._segments_defined(ctx)
        ))
        
        # 背景捕获 -> 阈值调整
        self.add_transition(StateTransition(
            CALIB_STATE_DIGIT_CAPTURE_BG, CALIB_STATE_DIGIT_ADJUST_THRESHOLD,
            condition=lambda ctx: self._background_captured(ctx)
        ))
        
        # 阈值调整 -> 开始（完成）
        self.add_transition(StateTransition(
            CALIB_STATE_DIGIT_ADJUST_THRESHOLD, CALIB_STATE_START,
            condition=lambda ctx: self._threshold_adjusted(ctx)
        ))
    
    def _setup_common_transitions(self):
        """设置通用转换"""
        # 任何状态都可以通过特定按键返回开始状态
        for state in [CALIB_STATE_BASE_POINTS_SELECT, CALIB_STATE_LED_ROI_SELECT,
                      CALIB_STATE_LED_SAMPLE_OFF, CALIB_STATE_LED_SAMPLE_ON,
                      CALIB_STATE_LED_ANALYZE, CALIB_STATE_LED_EDIT,
                      CALIB_STATE_DIGIT_CAPTURE_88, CALIB_STATE_DIGIT_ROI_SELECT_1,
                      CALIB_STATE_DIGIT_ROI_SELECT_2, CALIB_STATE_DIGIT_SEGMENT_SELECT,
                      CALIB_STATE_DIGIT_CAPTURE_BG, CALIB_STATE_DIGIT_ADJUST_THRESHOLD]:
            
            # Esc键返回开始状态
            self.add_transition(StateTransition(
                state, CALIB_STATE_START,
                condition=lambda ctx: self._key_pressed(27)  # Esc
            ))
    
    def setup_handlers(self):
        """设置状态处理器"""
        self.add_state_handler(CALIB_STATE_START, self._handle_start)
        self.add_state_handler(CALIB_STATE_BASE_POINTS_SELECT, self._handle_base_points)
        self.add_state_handler(CALIB_STATE_LED_ROI_SELECT, self._handle_led_roi_select)
        self.add_state_handler(CALIB_STATE_LED_SAMPLE_OFF, self._handle_led_sample_off)
        self.add_state_handler(CALIB_STATE_LED_SAMPLE_ON, self._handle_led_sample_on)
        self.add_state_handler(CALIB_STATE_LED_ANALYZE, self._handle_led_analyze)
        self.add_state_handler(CALIB_STATE_LED_EDIT, self._handle_led_edit)
        self.add_state_handler(CALIB_STATE_DIGIT_CAPTURE_88, self._handle_digit_capture_88)
        self.add_state_handler(CALIB_STATE_DIGIT_ROI_SELECT_1, self._handle_digit_roi_1)
        self.add_state_handler(CALIB_STATE_DIGIT_ROI_SELECT_2, self._handle_digit_roi_2)
        self.add_state_handler(CALIB_STATE_DIGIT_SEGMENT_SELECT, self._handle_digit_segments)
        self.add_state_handler(CALIB_STATE_DIGIT_CAPTURE_BG, self._handle_digit_background)
        self.add_state_handler(CALIB_STATE_DIGIT_ADJUST_THRESHOLD, self._handle_digit_threshold)
    
    def process_state(self, app_state) -> bool:
        """处理当前状态（重写基类方法以添加校准特定逻辑）"""
        # 检查相机状态
        if not self._check_camera(app_state):
            return False
        
        # 准备显示帧
        if not self._prepare_display_frame(app_state):
            return False
        
        # 获取按键
        from ui_handler import get_key
        self.last_key = get_key()
        
        # 处理ROI微调
        if handle_roi_fine_tune(app_state, self.last_key):
            pass  # 微调已处理
        
        # 调用基类的状态处理
        return super().process_state(app_state)
    
    def _check_camera(self, app_state) -> bool:
        """检查相机状态"""
        if app_state.cap is None or not app_state.cap.isOpened():
            app_state.prompt_message = "Error: Camera not initialized for calibration!"
            app_state.running = False
            return False
        return True
    
    def _prepare_display_frame(self, app_state) -> bool:
        """准备显示帧"""
        # 优先使用校准步骤中捕捉的静态图像
        frame_to_use = None
        
        if self.current_state in [CALIB_STATE_DIGIT_ROI_SELECT_1,
                                  CALIB_STATE_DIGIT_ROI_SELECT_2,
                                  CALIB_STATE_DIGIT_SEGMENT_SELECT] and \
           app_state.digit_calibration_image_88 is not None:
            frame_to_use = app_state.digit_calibration_image_88
        elif self.current_state == CALIB_STATE_DIGIT_ADJUST_THRESHOLD and \
             app_state.digit_background_image_off is not None:
            frame_to_use = app_state.digit_background_image_off
        
        # 如果没有静态图像，读取实时帧
        if frame_to_use is None:
            ret, frame = app_state.cap.read()
            if not ret or frame is None:
                app_state.prompt_message = "Error: Cannot read frame during calibration!"
                h, w = (480, 640)
                if app_state.display_frame is not None:
                    h, w = app_state.display_frame.shape[:2]
                app_state.display_frame = np.zeros((h, w, 3), dtype=np.uint8)
                self.frame_read_failed = True
                return False
            else:
                frame_to_use = frame
                app_state.current_frame = frame_to_use.copy()
                self.frame_read_failed = False
        
        app_state.display_frame = frame_to_use.copy()
        return True
    
    # 条件检查方法
    def _key_pressed(self, key_code) -> bool:
        """检查是否按下了指定按键"""
        return self.last_key == key_code
    
    def _has_led_rois(self, app_state) -> bool:
        """检查是否有已定义的LED ROI"""
        return any(roi for roi in app_state.led_rois)
    
    def _base_points_complete(self, app_state) -> bool:
        """检查基准点是否完成"""
        # 简化实现：检查是否有足够的基准点
        return sum(1 for point in app_state.base_points if point is not None) >= 2
    
    def _led_rois_defined(self, app_state) -> bool:
        """检查LED ROI是否已定义"""
        return any(roi for roi in app_state.led_rois)
    
    def _sampling_complete(self, app_state, sample_type) -> bool:
        """检查采样是否完成"""
        # 简化实现：检查采样数据
        if sample_type == 'off':
            return any(len(samples) > 0 for samples in app_state.led_off_state_samples)
        else:
            return any(len(samples) > 0 for samples in app_state.led_on_state_samples)
    
    def _analysis_complete(self, app_state) -> bool:
        """检查分析是否完成"""
        # 简化实现：检查阈值是否已计算
        return app_state.led_gray_threshold_green > 0
    
    def _digit_88_captured(self, app_state) -> bool:
        """检查数码管88图像是否已捕获"""
        return app_state.digit_calibration_image_88 is not None
    
    def _digit_roi_1_defined(self, app_state) -> bool:
        """检查数码管ROI1是否已定义"""
        return app_state.digit_rois[0] is not None
    
    def _digit_roi_2_defined(self, app_state) -> bool:
        """检查数码管ROI2是否已定义"""
        return app_state.digit_rois[1] is not None
    
    def _segments_defined(self, app_state) -> bool:
        """检查数码管段是否已定义"""
        # 简化实现
        return True
    
    def _background_captured(self, app_state) -> bool:
        """检查背景是否已捕获"""
        return app_state.digit_background_image_off is not None
    
    def _threshold_adjusted(self, app_state) -> bool:
        """检查阈值是否已调整"""
        return app_state.digit_threshold > 0
    
    # 状态处理方法（简化实现）
    def _handle_start(self, app_state) -> bool:
        """处理开始状态"""
        has_led_rois = self._has_led_rois(app_state)
        edit_hint = " | 'E' Edit LEDs" if has_led_rois else ""
        resample_hint = " | 'R' Resample LEDs" if has_led_rois else ""
        app_state.prompt_message = f"Calibration: 'B' Base Points | 'L' LED | 'D' Digit{edit_hint}{resample_hint} | 'S' Save&Exit | 'Enter' Detect"
        
        # 检查模式切换请求
        if self.last_key == 13:  # Enter
            app_state._mode_switch_requested = MODE_DETECTION
        elif self.last_key == ord('s'):
            config_manager.save_config(app_state)
            app_state._mode_switch_requested = MODE_DETECTION
        
        return True
    
    def _handle_base_points(self, app_state) -> bool:
        """处理基准点选择状态"""
        app_state.prompt_message = "Select base points by clicking. Press 'Enter' when done, 'Esc' to cancel."
        return True
    
    def _handle_led_roi_select(self, app_state) -> bool:
        """处理LED ROI选择状态"""
        app_state.prompt_message = "Select LED ROIs by dragging. Press 'Enter' when done, 'Esc' to cancel."
        return True
    
    def _handle_led_sample_off(self, app_state) -> bool:
        """处理LED OFF采样状态"""
        app_state.prompt_message = "Turn OFF all LEDs, then press 'Space' to sample."
        if self.last_key == ord(' '):
            # 执行OFF采样
            led_detector.led_sample_off_state(app_state.current_frame, app_state)
        return True
    
    def _handle_led_sample_on(self, app_state) -> bool:
        """处理LED ON采样状态"""
        app_state.prompt_message = "Turn ON all LEDs, then press 'Space' to sample."
        if self.last_key == ord(' '):
            # 执行ON采样
            led_detector.led_sample_on_state(app_state.current_frame, app_state)
        return True
    
    def _handle_led_analyze(self, app_state) -> bool:
        """处理LED分析状态"""
        app_state.prompt_message = "Press 'A' to analyze samples and calculate thresholds."
        if self.last_key == ord('a'):
            led_detector.led_calculate_thresholds(app_state)
            config_manager.save_config(app_state)
        return True
    
    def _handle_led_edit(self, app_state) -> bool:
        """处理LED编辑状态"""
        app_state.prompt_message = "LED Edit Mode: Click ROI to select, use arrow keys to move. 'Esc' to exit."
        return True
    
    def _handle_digit_capture_88(self, app_state) -> bool:
        """处理数码管88捕获状态"""
        app_state.prompt_message = "Display '88' on digit display, then press 'Space' to capture."
        if self.last_key == ord(' '):
            app_state.digit_calibration_image_88 = app_state.current_frame.copy()
        return True
    
    def _handle_digit_roi_1(self, app_state) -> bool:
        """处理数码管ROI1选择状态"""
        app_state.prompt_message = "Select first digit ROI by dragging. Press 'Enter' when done."
        return True
    
    def _handle_digit_roi_2(self, app_state) -> bool:
        """处理数码管ROI2选择状态"""
        app_state.prompt_message = "Select second digit ROI by dragging. Press 'Enter' when done."
        return True
    
    def _handle_digit_segments(self, app_state) -> bool:
        """处理数码管段选择状态"""
        app_state.prompt_message = "Select digit segments. Press 'Enter' when done."
        return True
    
    def _handle_digit_background(self, app_state) -> bool:
        """处理数码管背景捕获状态"""
        app_state.prompt_message = "Turn OFF digit display, then press 'Space' to capture background."
        if self.last_key == ord(' '):
            app_state.digit_background_image_off = app_state.current_frame.copy()
        return True
    
    def _handle_digit_threshold(self, app_state) -> bool:
        """处理数码管阈值调整状态"""
        app_state.prompt_message = "Adjust digit threshold with '+'/'-'. Press 'Enter' when done."
        if self.last_key == ord('+'):
            app_state.digit_threshold += 5
        elif self.last_key == ord('-'):
            app_state.digit_threshold -= 5
        elif self.last_key == 13:  # Enter
            config_manager.save_config(app_state)
        return True
    
    def can_exit_mode(self, app_state) -> bool:
        """检查是否可以退出校准模式"""
        # 校准模式通常可以随时退出，但可能需要保存
        return True
    
    def on_mode_enter(self, app_state):
        """进入校准模式时的处理"""
        logging.info("Entered Calibration mode")
        # 重置到开始状态
        self.reset_to_initial(CALIB_STATE_START)
        # 更新app_state中的校准状态
        app_state.current_calib_state = CALIB_STATE_START
    
    def on_mode_exit(self, app_state):
        """退出校准模式时的处理"""
        logging.info("Exited Calibration mode")
        # 清理临时状态
        self.frame_read_failed = False
        self.last_key = -1
