"""
测试事件处理模块
"""
import pytest
import numpy as np
import cv2
import sys
import os
import time
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from event_handlers.base_event_handler import BaseEventHandler, EventType, EventPriority
from event_handlers.event_manager import EventManager
from event_handlers.mouse_event_handler import Mouse<PERSON>ventHandler
from event_handlers.keyboard_event_handler import KeyboardEventHandler
from constants import *


class TestEventHandler(BaseEventHandler):
    """测试用的事件处理器实现"""
    
    def __init__(self, name="TestEventHandler"):
        super().__init__(name)
        self.add_supported_event(EventType.MOUSE_CLICK)
        self.add_supported_event(EventType.KEY_PRESS)
        self.handle_called = False
        self.handle_call_count = 0
        self.last_event_data = None
    
    def handle_event(self, event_type, event_data, app_state):
        """测试事件处理实现"""
        self.handle_called = True
        self.handle_call_count += 1
        self.last_event_data = event_data
        return True


@pytest.fixture
def mock_app_state():
    """创建模拟的应用状态"""
    app_state = Mock()
    app_state.current_mode = MODE_CALIBRATION
    app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
    app_state.running = True
    app_state.window_topmost = False
    app_state.selecting_roi = False
    app_state.current_rect = None
    app_state.template_preview_pos = None
    app_state.led_rois = [None] * 10
    app_state.led_max_rois = 10
    app_state.calib_led_roi_index = 0
    app_state.led_num_green = 5
    app_state.selected_roi_index = -1
    app_state.moving_roi = False
    app_state.template_mode = False
    app_state.fixed_template_mode = False
    app_state.current_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    app_state.display_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    app_state.status_message = ""
    return app_state


class TestBaseEventHandler:
    """测试事件处理器基类"""
    
    def test_event_handler_creation(self):
        """测试事件处理器创建"""
        handler = TestEventHandler("TestHandler")
        assert handler.name == "TestHandler"
        assert handler.enabled is True
        assert EventType.MOUSE_CLICK in handler.supported_events
        assert EventType.KEY_PRESS in handler.supported_events
    
    def test_event_handler_enable_disable(self, mock_app_state):
        """测试事件处理器启用/禁用"""
        handler = TestEventHandler()
        
        # 测试启用状态
        assert handler.enabled is True
        assert handler.can_handle_event(EventType.MOUSE_CLICK, mock_app_state) is True
        
        # 测试禁用
        handler.set_enabled(False)
        assert handler.enabled is False
        assert handler.can_handle_event(EventType.MOUSE_CLICK, mock_app_state) is False
    
    def test_event_processing(self, mock_app_state):
        """测试事件处理"""
        handler = TestEventHandler()
        
        event_data = {'x': 100, 'y': 200, 'cv_event': cv2.EVENT_LBUTTONDOWN}
        result = handler.process_event(EventType.MOUSE_CLICK, event_data, mock_app_state)
        
        assert result is True
        assert handler.handle_called is True
        assert handler.handle_call_count == 1
        assert handler.last_event_data == event_data
    
    def test_performance_stats(self):
        """测试性能统计"""
        handler = TestEventHandler()
        
        # 测试初始统计
        stats = handler.get_performance_stats()
        assert stats['name'] == "TestEventHandler"
        assert stats['event_count'] == 0
        assert stats['error_count'] == 0
        
        # 手动设置一些统计数据
        handler.event_count = 5
        handler.error_count = 1
        
        stats = handler.get_performance_stats()
        assert stats['event_count'] == 5
        assert stats['error_count'] == 1
        assert stats['error_rate'] == 0.2
        
        # 测试重置统计
        handler.reset_performance_stats()
        stats = handler.get_performance_stats()
        assert stats['event_count'] == 0
        assert stats['error_count'] == 0


class TestEventManager:
    """测试事件管理器"""
    
    def test_event_manager_creation(self):
        """测试事件管理器创建"""
        manager = EventManager()
        assert len(manager.handlers) == 0
        assert len(manager.handler_map) == 0
        assert manager.total_events_processed == 0
    
    def test_handler_management(self):
        """测试处理器管理"""
        manager = EventManager()
        handler = TestEventHandler("TestHandler1")
        
        # 测试添加处理器
        assert manager.add_handler(handler, EventPriority.NORMAL) is True
        assert len(manager.handlers) == 1
        assert "TestHandler1" in manager.handler_map
        
        # 测试获取处理器
        retrieved = manager.get_handler("TestHandler1")
        assert retrieved is handler
        
        # 测试列出处理器
        names = manager.list_handlers()
        assert "TestHandler1" in names
        
        # 测试移除处理器
        assert manager.remove_handler("TestHandler1") is True
        assert len(manager.handlers) == 0
        assert "TestHandler1" not in manager.handler_map
    
    def test_event_dispatch(self, mock_app_state):
        """测试事件分发"""
        manager = EventManager()
        handler = TestEventHandler("TestHandler")
        
        manager.add_handler(handler, EventPriority.NORMAL)
        
        # 测试事件分发
        event_data = {'x': 100, 'y': 200, 'cv_event': cv2.EVENT_LBUTTONDOWN}
        result = manager.dispatch_event(EventType.MOUSE_CLICK, event_data, mock_app_state)
        
        assert result is True
        assert handler.handle_called is True
    
    @patch('cv2.waitKey')
    def test_get_key_single_thread(self, mock_waitkey):
        """测试单线程模式获取按键"""
        manager = EventManager()
        mock_waitkey.return_value = ord('q')
        
        key = manager.get_key()
        assert key == ord('q')
        mock_waitkey.assert_called_once_with(1)


class TestMouseEventHandler:
    """测试鼠标事件处理器"""
    
    def test_mouse_handler_creation(self):
        """测试鼠标处理器创建"""
        handler = MouseEventHandler()
        assert handler.name == "MouseEventHandler"
        assert handler.enabled is True
        assert EventType.MOUSE_CLICK in handler.supported_events
        assert EventType.MOUSE_MOVE in handler.supported_events
        assert EventType.MOUSE_RELEASE in handler.supported_events
    
    def test_mouse_interaction_allowed(self, mock_app_state):
        """测试鼠标交互权限检查"""
        handler = MouseEventHandler()
        
        # 校准模式下应该允许
        mock_app_state.current_mode = MODE_CALIBRATION
        mock_app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
        assert handler._is_mouse_interaction_allowed(mock_app_state) is True
        
        # 检测模式下不应该允许
        mock_app_state.current_mode = MODE_DETECTION
        assert handler._is_mouse_interaction_allowed(mock_app_state) is False
    
    def test_roi_selection_event(self, mock_app_state):
        """测试ROI选择事件"""
        handler = MouseEventHandler()
        
        # 模拟鼠标按下事件
        event_data = {
            'x': 100, 'y': 200, 
            'cv_event': cv2.EVENT_LBUTTONDOWN,
            'flags': 0
        }
        
        result = handler.handle_event(EventType.MOUSE_CLICK, event_data, mock_app_state)
        assert result is True
    
    def test_mouse_state_management(self):
        """测试鼠标状态管理"""
        handler = MouseEventHandler()
        
        # 测试初始状态
        state = handler.get_mouse_state()
        assert state['is_dragging'] is False
        assert state['selecting_roi'] is False
        assert state['moving_roi'] is False
        
        # 测试状态重置
        handler.selecting_roi = True
        handler.moving_roi = True
        handler.reset_mouse_state()
        
        state = handler.get_mouse_state()
        assert state['selecting_roi'] is False
        assert state['moving_roi'] is False


class TestKeyboardEventHandler:
    """测试键盘事件处理器"""
    
    def test_keyboard_handler_creation(self):
        """测试键盘处理器创建"""
        handler = KeyboardEventHandler()
        assert handler.name == "KeyboardEventHandler"
        assert handler.enabled is True
        assert EventType.KEY_PRESS in handler.supported_events
    
    @patch('cv2.waitKey')
    def test_get_key_single_thread(self, mock_waitkey):
        """测试单线程模式获取按键"""
        handler = KeyboardEventHandler()
        mock_waitkey.return_value = ord('q')
        
        key = handler.get_key()
        assert key == ord('q')
        mock_waitkey.assert_called_once_with(1)
    
    def test_key_event_processing(self, mock_app_state):
        """测试按键事件处理"""
        handler = KeyboardEventHandler()
        
        # 测试退出按键
        event_data = {'key': ord('q')}
        result = handler.handle_event(EventType.KEY_PRESS, event_data, mock_app_state)
        
        assert result is True
        assert mock_app_state.running is False
    
    def test_roi_adjustment(self, mock_app_state):
        """测试ROI微调"""
        handler = KeyboardEventHandler()
        mock_app_state.current_rect = (100, 100, 50, 50)
        
        # 测试向右微调
        result = handler._adjust_current_roi(mock_app_state, 5, 0)
        assert result is True
        assert mock_app_state.current_rect == (105, 100, 50, 50)
        
        # 测试向下微调
        result = handler._adjust_current_roi(mock_app_state, 0, 5)
        assert result is True
        assert mock_app_state.current_rect == (105, 105, 50, 50)


class TestEventHandlersIntegration:
    """事件处理系统集成测试"""
    
    @pytest.mark.integration
    def test_full_event_system(self, mock_app_state):
        """测试完整事件系统"""
        # 创建事件管理器和处理器
        manager = EventManager()
        mouse_handler = MouseEventHandler()
        keyboard_handler = KeyboardEventHandler()
        
        # 添加处理器到管理器
        manager.add_handler(mouse_handler, EventPriority.HIGH)
        manager.add_handler(keyboard_handler, EventPriority.NORMAL)
        
        # 测试鼠标事件处理
        mouse_event_data = {
            'x': 100, 'y': 200, 
            'cv_event': cv2.EVENT_LBUTTONDOWN,
            'flags': 0
        }
        result = manager.dispatch_event(EventType.MOUSE_CLICK, mouse_event_data, mock_app_state)
        assert result is True
        
        # 测试键盘事件处理
        keyboard_event_data = {'key': ord('q')}
        result = manager.dispatch_event(EventType.KEY_PRESS, keyboard_event_data, mock_app_state)
        assert result is True
        
        # 验证统计信息
        stats = manager.get_performance_stats()
        assert stats['total_events_processed'] == 2
        assert stats['handler_count'] == 2
        assert stats['enabled_handler_count'] == 2
    
    @pytest.mark.integration
    @pytest.mark.performance
    def test_event_system_performance(self, mock_app_state):
        """测试事件系统性能"""
        manager = EventManager()
        mouse_handler = MouseEventHandler()
        keyboard_handler = KeyboardEventHandler()
        
        manager.add_handler(mouse_handler)
        manager.add_handler(keyboard_handler)
        
        # 性能测试
        start_time = time.time()
        for i in range(100):
            # 交替发送鼠标和键盘事件
            if i % 2 == 0:
                event_data = {'x': i, 'y': i, 'cv_event': cv2.EVENT_LBUTTONDOWN, 'flags': 0}
                manager.dispatch_event(EventType.MOUSE_CLICK, event_data, mock_app_state)
            else:
                event_data = {'key': ord('a')}
                manager.dispatch_event(EventType.KEY_PRESS, event_data, mock_app_state)
        end_time = time.time()
        
        total_time = end_time - start_time
        events_per_second = 100 / total_time if total_time > 0 else float('inf')

        # 验证性能满足要求（应该能处理至少100事件/秒）
        assert events_per_second > 100 or total_time < 0.01  # 如果时间太短，认为性能很好
        
        # 验证统计信息
        stats = manager.get_performance_stats()
        assert stats['total_events_processed'] == 100
