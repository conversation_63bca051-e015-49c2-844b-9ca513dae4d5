"""
渲染器基类

提供渲染器的基础功能，包括缓存机制、更新频率控制、启用/禁用等。
所有具体的渲染器都应该继承这个基类。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
import numpy as np
import time
import logging


class BaseRenderer(ABC):
    """渲染器基类"""
    
    def __init__(self, name: str = "BaseRenderer"):
        """
        初始化渲染器
        
        Args:
            name: 渲染器名称，用于日志和调试
        """
        self.name = name
        self.enabled = True
        
        # 缓存相关
        self.cache: Dict[str, Any] = {}
        self.cache_enabled = True
        self.cache_max_age = 1.0  # 缓存最大存活时间（秒）
        
        # 更新频率控制
        self.last_update_time = 0
        self.update_interval = 0.1  # 默认100ms更新一次（10Hz）
        self.force_update = False
        
        # 性能统计
        self.render_count = 0
        self.total_render_time = 0.0
        self.last_render_time = 0.0
        
        logging.debug(f"Renderer '{self.name}' initialized")
    
    @abstractmethod
    def render(self, frame: np.ndarray, app_state: Any) -> np.ndarray:
        """
        渲染到帧上
        
        Args:
            frame: 输入帧
            app_state: 应用状态
            
        Returns:
            渲染后的帧
        """
        pass
    
    def should_update(self, current_time: Optional[float] = None) -> bool:
        """
        检查是否需要更新
        
        Args:
            current_time: 当前时间，如果为None则使用time.time()
            
        Returns:
            是否需要更新
        """
        if not self.enabled:
            return False
        
        if self.force_update:
            self.force_update = False
            return True
        
        if current_time is None:
            current_time = time.time()
        
        return (current_time - self.last_update_time) >= self.update_interval
    
    def set_enabled(self, enabled: bool):
        """设置是否启用"""
        if self.enabled != enabled:
            self.enabled = enabled
            if enabled:
                logging.debug(f"Renderer '{self.name}' enabled")
                self.force_update = True  # 启用时强制更新
            else:
                logging.debug(f"Renderer '{self.name}' disabled")
                self.clear_cache()  # 禁用时清空缓存
    
    def set_update_interval(self, interval: float):
        """
        设置更新间隔
        
        Args:
            interval: 更新间隔（秒）
        """
        self.update_interval = max(0.001, interval)  # 最小1ms间隔
        logging.debug(f"Renderer '{self.name}' update interval set to {self.update_interval:.3f}s")
    
    def force_update_next(self):
        """强制下次更新"""
        self.force_update = True
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        logging.debug(f"Renderer '{self.name}' cache cleared")
    
    def get_cache(self, key: str, default: Any = None) -> Any:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            缓存值或默认值
        """
        if not self.cache_enabled:
            return default
        
        if key not in self.cache:
            return default
        
        cache_entry = self.cache[key]
        
        # 检查缓存是否过期
        if isinstance(cache_entry, dict) and 'timestamp' in cache_entry:
            age = time.time() - cache_entry['timestamp']
            if age > self.cache_max_age:
                del self.cache[key]
                return default
            return cache_entry.get('value', default)
        
        return cache_entry
    
    def set_cache(self, key: str, value: Any, with_timestamp: bool = True):
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            with_timestamp: 是否添加时间戳
        """
        if not self.cache_enabled:
            return
        
        if with_timestamp:
            self.cache[key] = {
                'value': value,
                'timestamp': time.time()
            }
        else:
            self.cache[key] = value
    
    def render_with_timing(self, frame: np.ndarray, app_state: Any) -> np.ndarray:
        """
        带性能统计的渲染
        
        Args:
            frame: 输入帧
            app_state: 应用状态
            
        Returns:
            渲染后的帧
        """
        if not self.enabled:
            return frame
        
        current_time = time.time()
        
        # 检查是否需要更新
        if not self.should_update(current_time):
            # 如果有缓存的结果，使用缓存
            cached_frame = self.get_cache('last_rendered_frame')
            if cached_frame is not None and cached_frame.shape == frame.shape:
                return cached_frame
        
        # 执行渲染
        start_time = time.time()
        
        try:
            rendered_frame = self.render(frame, app_state)
            
            # 更新性能统计
            render_time = time.time() - start_time
            self.last_render_time = render_time
            self.total_render_time += render_time
            self.render_count += 1
            
            # 更新时间戳
            self.last_update_time = current_time
            
            # 缓存结果
            if self.cache_enabled:
                self.set_cache('last_rendered_frame', rendered_frame.copy())
            
            return rendered_frame
            
        except Exception as e:
            logging.error(f"Renderer '{self.name}' failed: {e}")
            return frame
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            性能统计字典
        """
        avg_render_time = (self.total_render_time / self.render_count 
                          if self.render_count > 0 else 0)
        
        return {
            'name': self.name,
            'enabled': self.enabled,
            'render_count': self.render_count,
            'total_render_time': self.total_render_time,
            'average_render_time': avg_render_time,
            'last_render_time': self.last_render_time,
            'update_interval': self.update_interval,
            'cache_size': len(self.cache),
            'fps': 1.0 / avg_render_time if avg_render_time > 0 else 0
        }
    
    def reset_performance_stats(self):
        """重置性能统计"""
        self.render_count = 0
        self.total_render_time = 0.0
        self.last_render_time = 0.0
        logging.debug(f"Renderer '{self.name}' performance stats reset")
    
    def validate_frame(self, frame: np.ndarray) -> bool:
        """
        验证帧的有效性
        
        Args:
            frame: 输入帧
            
        Returns:
            帧是否有效
        """
        if frame is None:
            return False
        
        if not isinstance(frame, np.ndarray):
            return False
        
        if len(frame.shape) != 3:
            return False
        
        if frame.shape[2] != 3:  # 期望BGR格式
            return False
        
        return True
    
    def get_frame_info(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        获取帧信息
        
        Args:
            frame: 输入帧
            
        Returns:
            帧信息字典
        """
        if not self.validate_frame(frame):
            return {'valid': False}
        
        h, w, c = frame.shape
        return {
            'valid': True,
            'height': h,
            'width': w,
            'channels': c,
            'dtype': str(frame.dtype),
            'size_mb': frame.nbytes / (1024 * 1024)
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Renderer(name='{self.name}', enabled={self.enabled})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        stats = self.get_performance_stats()
        return (f"Renderer(name='{self.name}', enabled={self.enabled}, "
                f"renders={stats['render_count']}, "
                f"avg_time={stats['average_render_time']:.3f}s)")
    
    def cleanup(self):
        """清理资源"""
        self.clear_cache()
        self.reset_performance_stats()
        logging.debug(f"Renderer '{self.name}' cleaned up")
