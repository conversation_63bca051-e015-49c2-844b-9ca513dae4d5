"""
事件处理模块

该模块负责管理所有用户交互事件的处理，包括：
- 鼠标事件（点击、拖拽、滚轮等）
- 键盘事件（按键、组合键等）
- 事件分发和路由
- 事件处理器的生命周期管理

主要组件：
- BaseEventHandler: 事件处理器基类
- MouseEventHandler: 鼠标事件处理器
- KeyboardEventHandler: 键盘事件处理器
- EventManager: 事件管理器
"""

from .base_event_handler import BaseEventHandler, EventType, EventPriority
from .event_manager import EventManager
from .mouse_event_handler import MouseEventHandler
from .keyboard_event_handler import KeyboardEventHandler

__all__ = [
    'BaseEventHandler',
    'EventType',
    'EventPriority',
    'EventManager',
    'MouseEventHandler',
    'KeyboardEventHandler',
]

__version__ = '1.0.0'
__author__ = 'Event Handler Refactor Team'
