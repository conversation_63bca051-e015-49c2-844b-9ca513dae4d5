# 单元测试框架使用指南

## 测试框架概述

本项目使用pytest作为测试框架，为ui_handler.py重构项目提供全面的测试覆盖。

## 测试结构

```
tests/
├── __init__.py                    # 测试模块初始化
├── conftest.py                    # pytest配置和共享fixtures
├── test_ui_handler_original.py    # 原始ui_handler功能测试
├── test_state_machine.py          # 状态机模块测试
├── test_ui_components.py          # UI组件模块测试
├── test_event_handlers.py         # 事件处理模块测试
├── test_core_logic.py             # 核心逻辑模块测试
├── test_integration.py            # 集成测试
└── run_tests.py                   # 测试运行脚本
```

## 测试分类

### 测试标记
- `@pytest.mark.unit` - 单元测试
- `@pytest.mark.integration` - 集成测试
- `@pytest.mark.performance` - 性能测试
- `@pytest.mark.slow` - 慢速测试
- `@pytest.mark.ui` - UI相关测试
- `@pytest.mark.core` - 核心算法测试
- `@pytest.mark.regression` - 回归测试

### 测试类型说明

#### 1. 单元测试 (unit)
- 测试单个函数或类的功能
- 使用Mock隔离依赖
- 运行速度快，覆盖率高

#### 2. 集成测试 (integration)
- 测试模块间的协同工作
- 验证接口兼容性
- 测试完整工作流程

#### 3. 性能测试 (performance)
- 验证重构后性能不下降
- 监控内存使用和执行时间
- 确保实时性能要求

#### 4. 核心算法测试 (core)
- 重点测试LED检测、数码管识别等核心算法
- 确保重构不影响算法准确性
- 高优先级测试

#### 5. 回归测试 (regression)
- 确保重构不破坏现有功能
- 对比重构前后的行为
- 验证向后兼容性

## 共享Fixtures

### 基础Fixtures
- `mock_app_state` - 模拟的AppState实例
- `mock_camera` - 模拟的摄像头对象
- `sample_frame` - 示例图像帧
- `mock_shared_state` - 模拟的共享状态对象

### 检测器Fixtures
- `mock_led_detector` - 模拟的LED检测器
- `mock_digit_detector` - 模拟的数码管检测器
- `mock_config_manager` - 模拟的配置管理器

### UI相关Fixtures
- `mock_cv2` - 模拟的OpenCV函数
- `test_helper` - 测试辅助类

### 性能测试Fixtures
- `performance_threshold` - 性能测试阈值
- `integration_test_config` - 集成测试配置

## 运行测试

### 使用测试脚本
```bash
# 检查测试环境
python tests/run_tests.py --check

# 运行快速测试（默认）
python tests/run_tests.py

# 运行特定类型的测试
python tests/run_tests.py --type unit
python tests/run_tests.py --type integration
python tests/run_tests.py --type performance
python tests/run_tests.py --type core

# 运行特定测试文件
python tests/run_tests.py --file tests/test_ui_handler_original.py

# 生成覆盖率报告
python tests/run_tests.py --coverage
```

### 直接使用pytest
```bash
# 运行所有测试
pytest

# 运行特定标记的测试
pytest -m unit
pytest -m integration
pytest -m "core and not slow"

# 运行特定文件
pytest tests/test_ui_handler_original.py

# 生成覆盖率报告
pytest --cov=. --cov-report=html

# 详细输出
pytest -v -s

# 只运行失败的测试
pytest --lf
```

## 编写测试

### 测试命名规范
- 测试文件：`test_*.py`
- 测试类：`Test*`
- 测试函数：`test_*`

### 测试结构示例
```python
class TestModuleName:
    """测试模块名称"""
    
    def test_function_basic_functionality(self, mock_app_state):
        """测试基本功能"""
        # Arrange
        # 准备测试数据
        
        # Act
        # 执行被测试的功能
        
        # Assert
        # 验证结果
        pass
    
    @pytest.mark.performance
    def test_function_performance(self, performance_threshold):
        """测试性能"""
        # 性能测试逻辑
        pass
    
    @pytest.mark.integration
    def test_function_integration(self, mock_app_state):
        """测试集成功能"""
        # 集成测试逻辑
        pass
```

### Mock使用指南
```python
from unittest.mock import Mock, patch

# Mock对象
mock_obj = Mock()
mock_obj.method.return_value = "expected_value"

# Mock函数
@patch('module.function')
def test_with_mock(mock_function):
    mock_function.return_value = "mocked_result"
    # 测试逻辑

# Mock类
@patch('module.ClassName')
def test_with_mock_class(MockClass):
    mock_instance = MockClass.return_value
    mock_instance.method.return_value = "result"
    # 测试逻辑
```

## 测试数据管理

### 测试数据目录
```
tests/
├── test_data/          # 测试数据目录
│   ├── images/         # 测试图像
│   ├── configs/        # 测试配置文件
│   └── logs/           # 测试日志文件
```

### 使用测试数据
```python
def test_with_test_data(test_data_dir):
    """使用测试数据的测试"""
    image_path = test_data_dir / "images" / "test_frame.jpg"
    config_path = test_data_dir / "configs" / "test_config.json"
    # 使用测试数据
```

## 性能测试

### 性能阈值配置
```python
performance_threshold = {
    'max_processing_time': 0.1,    # 最大处理时间100ms
    'max_memory_increase': 0.1,    # 最大内存增长10%
    'min_fps': 25.0,               # 最小帧率25FPS
}
```

### 性能测试示例
```python
@pytest.mark.performance
def test_function_performance(performance_threshold):
    """测试函数性能"""
    import time
    import psutil
    
    # 记录开始状态
    start_time = time.time()
    start_memory = psutil.Process().memory_info().rss
    
    # 执行测试
    for _ in range(100):
        function_under_test()
    
    # 计算性能指标
    end_time = time.time()
    end_memory = psutil.Process().memory_info().rss
    
    avg_time = (end_time - start_time) / 100
    memory_increase = (end_memory - start_memory) / start_memory
    
    # 验证性能要求
    assert avg_time < performance_threshold['max_processing_time']
    assert memory_increase < performance_threshold['max_memory_increase']
```

## 持续集成

### 测试自动化
- 每次代码提交自动运行快速测试
- 每日运行完整测试套件
- 性能测试定期运行

### 测试报告
- 生成HTML覆盖率报告
- 性能基准报告
- 测试结果趋势分析

## 最佳实践

### 1. 测试隔离
- 每个测试独立运行
- 使用fixtures提供干净的测试环境
- 避免测试间的依赖

### 2. 测试可读性
- 使用描述性的测试名称
- 添加清晰的文档字符串
- 遵循AAA模式（Arrange-Act-Assert）

### 3. 测试覆盖率
- 核心逻辑覆盖率 > 90%
- UI组件覆盖率 > 80%
- 总体覆盖率 > 85%

### 4. 测试维护
- 定期更新测试用例
- 移除过时的测试
- 重构测试代码

## 故障排除

### 常见问题
1. **导入错误**：检查PYTHONPATH设置
2. **Mock失效**：确认Mock的路径正确
3. **测试超时**：增加超时时间或优化测试逻辑
4. **依赖冲突**：使用虚拟环境隔离依赖

### 调试技巧
```python
# 在测试中添加调试信息
def test_debug_example():
    result = function_under_test()
    print(f"Debug: result = {result}")  # 使用 pytest -s 查看输出
    assert result == expected_value

# 使用pytest的调试功能
pytest --pdb  # 在失败时进入调试器
pytest --trace  # 在每个测试开始时进入调试器
```

---

**注意**：这个测试框架将在重构过程中逐步完善，确保每个重构步骤都有相应的测试验证。
