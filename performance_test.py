"""
重构后系统性能测试

测试重构后系统的性能指标，包括：
- 内存使用
- 执行时间
- 模块加载时间
- 功能完整性验证
"""

import time
import psutil
import os
import sys
import tracemalloc
import gc
from typing import Dict, Any
import numpy as np
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app_state import AppState
from constants import *


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.process = psutil.Process(os.getpid())
    
    def start_memory_tracking(self):
        """开始内存跟踪"""
        tracemalloc.start()
        gc.collect()  # 强制垃圾回收
        return self.process.memory_info().rss / 1024 / 1024  # MB
    
    def stop_memory_tracking(self):
        """停止内存跟踪并返回内存使用情况"""
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        rss_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        return {
            'current_traced': current / 1024 / 1024,  # MB
            'peak_traced': peak / 1024 / 1024,  # MB
            'rss_memory': rss_memory
        }
    
    def measure_import_time(self, module_name: str):
        """测量模块导入时间"""
        start_time = time.perf_counter()
        try:
            __import__(module_name)
            end_time = time.perf_counter()
            return {
                'success': True,
                'import_time': end_time - start_time,
                'error': None
            }
        except Exception as e:
            end_time = time.perf_counter()
            return {
                'success': False,
                'import_time': end_time - start_time,
                'error': str(e)
            }


def create_mock_app_state():
    """创建模拟的应用状态"""
    app_state = AppState()
    
    # 设置基本状态
    app_state.current_mode = MODE_CALIBRATION
    app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
    app_state.running = True
    
    # 设置帧数据
    app_state.current_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    app_state.display_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 设置ROI数据
    app_state.led_rois = [None] * 10
    app_state.led_max_rois = 10
    app_state.calib_led_roi_index = 0
    app_state.led_num_green = 5
    
    # 设置时间相关
    app_state.prev_time = time.time()
    app_state.fps = 30.0
    app_state.last_task_cleanup_time = time.time()
    
    # 设置共享状态
    app_state.shared_state = Mock()
    app_state.shared_state.get_and_clear_key.return_value = -1
    
    return app_state


def test_module_imports():
    """测试各个模块的导入性能"""
    profiler = PerformanceProfiler()
    modules_to_test = [
        'state_machine',
        'ui_components', 
        'event_handlers',
        'ui_handler_refactored'
    ]
    
    results = {}
    print("测试模块导入性能...")
    
    for module in modules_to_test:
        result = profiler.measure_import_time(module)
        results[module] = result
        status = "成功" if result['success'] else f"失败: {result['error']}"
        print(f"  {module}: {result['import_time']:.4f}s ({status})")
    
    return results


def test_refactored_system():
    """测试重构后系统性能"""
    profiler = PerformanceProfiler()
    results = {}
    
    print("\n测试重构后系统性能...")
    
    # 导入重构后的UI处理器
    import ui_handler_refactored as ui_handler
    
    # 创建应用状态
    app_state = create_mock_app_state()
    
    # 测试内存使用
    start_memory = profiler.start_memory_tracking()
    
    # 初始化UI处理器
    ui_handler.set_shared_state(app_state.shared_state)
    
    # 测试核心功能
    iterations = 100
    mock_task_manager = Mock()
    mock_task_manager.cleanup_old_tasks.return_value = None
    
    start_time = time.perf_counter()
    
    for i in range(iterations):
        try:
            # 模拟核心处理逻辑
            with patch('async_task_manager.get_task_manager', return_value=mock_task_manager):
                ui_handler.process_core_logic(app_state)
            
            # 模拟显示帧准备
            ui_handler.prepare_display_frame(app_state)
            
        except Exception as e:
            print(f"  执行错误: {e}")
            break
    
    end_time = time.perf_counter()
    execution_time = end_time - start_time
    
    memory_info = profiler.stop_memory_tracking()
    
    # 获取性能统计
    try:
        perf_stats = ui_handler.get_performance_stats()
        results['ui_stats'] = perf_stats
    except Exception as e:
        print(f"  获取性能统计失败: {e}")
        results['ui_stats'] = None
    
    results.update({
        'memory': memory_info,
        'iterations': iterations,
        'avg_execution_time': execution_time / iterations if iterations > 0 else 0,
        'total_execution_time': execution_time
    })
    
    print(f"  内存使用: {memory_info['rss_memory']:.2f}MB")
    print(f"  平均执行时间: {results['avg_execution_time']*1000:.2f}ms")
    print(f"  理论最大FPS: {1000/(results['avg_execution_time']*1000):.1f}" if results['avg_execution_time'] > 0 else "  理论最大FPS: 无限")
    
    # 清理
    ui_handler.cleanup()
    
    return results


def generate_performance_report(module_results: Dict, system_results: Dict):
    """生成性能报告"""
    print("\n" + "="*60)
    print("重构项目性能报告")
    print("="*60)
    
    # 模块化架构
    print("1. 模块化架构:")
    successful_modules = sum(1 for r in module_results.values() if r['success'])
    total_modules = len(module_results)
    print(f"   - 成功加载模块: {successful_modules}/{total_modules}")
    
    total_import_time = sum(r['import_time'] for r in module_results.values())
    print(f"   - 总模块导入时间: {total_import_time:.4f}s")
    
    for module, result in module_results.items():
        if result['success']:
            print(f"   ✓ {module}: {result['import_time']:.4f}s")
        else:
            print(f"   ✗ {module}: 导入失败")
    print()
    
    # 系统性能
    print("2. 系统性能:")
    if 'avg_execution_time' in system_results:
        avg_time = system_results['avg_execution_time'] * 1000
        print(f"   - 平均处理时间: {avg_time:.2f}ms")
        print(f"   - 理论最大FPS: {1000/avg_time:.1f}" if avg_time > 0 else "   - 理论最大FPS: 无限")
        print(f"   - 测试迭代次数: {system_results['iterations']}")
    
    if 'memory' in system_results:
        memory = system_results['memory']['rss_memory']
        peak_memory = system_results['memory']['peak_traced']
        print(f"   - RSS内存使用: {memory:.2f}MB")
        print(f"   - 峰值内存使用: {peak_memory:.2f}MB")
    print()
    
    # 组件状态
    print("3. 组件状态:")
    ui_stats = system_results.get('ui_stats', {})
    if ui_stats and ui_stats.get('initialized'):
        print("   ✓ 管理器初始化: 成功")
        
        if ui_stats.get('state_machine_manager'):
            sm_stats = ui_stats['state_machine_manager']
            print(f"   ✓ 状态机管理器: 当前模式 {sm_stats.get('current_mode', 'Unknown')}")
        
        if ui_stats.get('display_manager'):
            dm_stats = ui_stats['display_manager']
            print(f"   ✓ 显示管理器: {dm_stats.get('renderer_count', 0)} 个渲染器")
        
        if ui_stats.get('event_manager'):
            em_stats = ui_stats['event_manager']
            print(f"   ✓ 事件管理器: {em_stats.get('handler_count', 0)} 个处理器")
    else:
        print("   ✗ 管理器初始化: 失败")
    print()
    
    # 架构优势
    print("4. 架构优势:")
    print("   ✓ 模块化设计: 功能独立封装")
    print("   ✓ 单一职责: 每个类职责明确")
    print("   ✓ 可测试性: 每个模块独立测试")
    print("   ✓ 可扩展性: 易于添加新功能")
    print("   ✓ 可维护性: 代码结构清晰")
    print("   ✓ 向后兼容: 保持原有接口")
    print()
    
    # 性能评估
    print("5. 性能评估:")
    if 'avg_execution_time' in system_results:
        avg_time = system_results['avg_execution_time'] * 1000
        if avg_time < 10:
            print("   ✓ 执行性能: 优秀 (<10ms)")
        elif avg_time < 33:
            print("   ✓ 执行性能: 良好 (<33ms, 支持30FPS)")
        elif avg_time < 50:
            print("   ○ 执行性能: 一般 (<50ms, 支持20FPS)")
        else:
            print("   ✗ 执行性能: 需要优化 (>50ms)")
    
    if 'memory' in system_results:
        memory = system_results['memory']['rss_memory']
        if memory < 100:
            print("   ✓ 内存使用: 优秀 (<100MB)")
        elif memory < 200:
            print("   ✓ 内存使用: 良好 (<200MB)")
        elif memory < 500:
            print("   ○ 内存使用: 一般 (<500MB)")
        else:
            print("   ✗ 内存使用: 需要优化 (>500MB)")
    print()


def main():
    """主函数"""
    print("重构后系统性能测试")
    print("="*60)
    
    # 测试模块导入
    module_results = test_module_imports()
    
    # 测试系统性能
    try:
        system_results = test_refactored_system()
    except Exception as e:
        print(f"系统测试失败: {e}")
        system_results = {'error': str(e)}
    
    # 生成报告
    if 'error' not in system_results:
        generate_performance_report(module_results, system_results)
    else:
        print(f"由于测试失败，无法生成完整报告: {system_results['error']}")
    
    print("性能测试完成")


if __name__ == "__main__":
    main()
