#!/usr/bin/env python3
"""
数码管显示推送功能测试脚本
测试数码管检测结果是否能正确推送到WebSocket
"""

import sys
import os
import time
import threading
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from simple_websocket_server import simple_websocket_server
from digit_detector import _push_digit_display_content

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_digit_display_push():
    """测试数码管显示推送功能"""
    print("=" * 60)
    print("数码管显示推送功能测试")
    print("=" * 60)
    
    # 启动WebSocket服务器
    print("1. 启动WebSocket服务器...")
    try:
        server_thread = threading.Thread(target=simple_websocket_server.start_server, daemon=True)
        server_thread.start()
        time.sleep(2)  # 等待服务器启动
        
        if simple_websocket_server.running:
            print("   ✅ WebSocket服务器启动成功")
        else:
            print("   ❌ WebSocket服务器启动失败")
            return False
            
    except Exception as e:
        print(f"   ❌ WebSocket服务器启动异常: {e}")
        return False
    
    # 测试不同的数码管显示内容
    test_cases = [
        (['8', '8'], "88"),
        (['0', '0'], "00"),
        (['1', '2'], "12"),
        (['F', 'F'], "FF"),
        ([None, None], "--"),
        (['8', None], "8-"),
        ([None, '8'], "-8"),
        (['?', '?'], "??"),
    ]
    
    print("\n2. 测试数码管内容推送...")
    for i, (recognized_chars, expected_display) in enumerate(test_cases, 1):
        print(f"   测试 {i}: {recognized_chars} -> 期望显示: {expected_display}")
        
        try:
            # 调用推送函数
            _push_digit_display_content(recognized_chars)
            print(f"   ✅ 推送成功")
            time.sleep(0.5)  # 短暂延迟
            
        except Exception as e:
            print(f"   ❌ 推送失败: {e}")
    
    # 测试连续推送
    print("\n3. 测试连续推送（模拟实际检测）...")
    continuous_test_data = [
        ['0', '0'], ['0', '1'], ['0', '2'], ['0', '3'], ['0', '4'],
        ['0', '5'], ['0', '6'], ['0', '7'], ['0', '8'], ['0', '9'],
        ['1', '0'], ['1', '1'], ['1', '2'], ['8', '8']
    ]
    
    for i, chars in enumerate(continuous_test_data):
        display_value = ''.join(str(c) if c is not None else '-' for c in chars)
        print(f"   推送 {i+1:2d}: {display_value}")
        
        try:
            _push_digit_display_content(chars)
            time.sleep(0.2)  # 模拟检测间隔
        except Exception as e:
            print(f"   ❌ 推送失败: {e}")
    
    print("\n4. 测试完成")
    print("   💡 请在网页端查看数码管显示是否正确更新")
    print("   💡 最后应该显示 '88' 并触发检测倒计时")
    
    # 保持服务器运行一段时间
    print("\n5. 保持服务器运行30秒，请在网页端验证...")
    for remaining in range(30, 0, -1):
        print(f"   剩余时间: {remaining}秒", end='\r')
        time.sleep(1)
    
    print("\n\n6. 停止WebSocket服务器...")
    try:
        simple_websocket_server.stop_server()
        print("   ✅ WebSocket服务器已停止")
    except Exception as e:
        print(f"   ⚠️  停止服务器时出现异常: {e}")
    
    print("\n测试完成！")
    return True

def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("错误处理测试")
    print("=" * 60)
    
    # 测试在服务器未运行时推送
    print("1. 测试服务器未运行时的推送...")
    try:
        _push_digit_display_content(['8', '8'])
        print("   ✅ 错误处理正常（静默失败）")
    except Exception as e:
        print(f"   ❌ 错误处理异常: {e}")
    
    # 测试异常数据
    print("2. 测试异常数据推送...")
    test_cases = [
        None,
        [],
        ['a', 'b', 'c', 'd', 'e'],  # 超长数组
        [1, 2, 3],  # 数字类型
        ['', ''],  # 空字符串
    ]
    
    for i, test_data in enumerate(test_cases, 1):
        print(f"   测试 {i}: {test_data}")
        try:
            _push_digit_display_content(test_data)
            print(f"   ✅ 处理正常")
        except Exception as e:
            print(f"   ⚠️  处理异常: {e}")

if __name__ == "__main__":
    try:
        # 运行主要测试
        success = test_digit_display_push()
        
        # 运行错误处理测试
        test_error_handling()
        
        if success:
            print("\n🎉 所有测试完成！")
            print("💡 如果网页端数码管显示正确更新，说明功能正常工作")
        else:
            print("\n❌ 测试失败，请检查配置")
            
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        try:
            simple_websocket_server.stop_server()
        except:
            pass
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
