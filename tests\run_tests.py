#!/usr/bin/env python3
"""
测试运行脚本
提供不同类型测试的快速运行方式
"""
import sys
import os
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"运行: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✓ {description} 成功完成")
        else:
            print(f"✗ {description} 失败 (返回码: {result.returncode})")
        
        return result.returncode == 0
    
    except Exception as e:
        print(f"✗ 运行 {description} 时出错: {e}")
        return False


def run_unit_tests():
    """运行单元测试"""
    cmd = ["python", "-m", "pytest", "-m", "unit", "-v"]
    return run_command(cmd, "单元测试")


def run_integration_tests():
    """运行集成测试"""
    cmd = ["python", "-m", "pytest", "-m", "integration", "-v"]
    return run_command(cmd, "集成测试")


def run_performance_tests():
    """运行性能测试"""
    cmd = ["python", "-m", "pytest", "-m", "performance", "-v"]
    return run_command(cmd, "性能测试")


def run_core_tests():
    """运行核心算法测试"""
    cmd = ["python", "-m", "pytest", "-m", "core", "-v"]
    return run_command(cmd, "核心算法测试")


def run_ui_tests():
    """运行UI测试"""
    cmd = ["python", "-m", "pytest", "-m", "ui", "-v"]
    return run_command(cmd, "UI测试")


def run_regression_tests():
    """运行回归测试"""
    cmd = ["python", "-m", "pytest", "-m", "regression", "-v"]
    return run_command(cmd, "回归测试")


def run_all_tests():
    """运行所有测试"""
    cmd = ["python", "-m", "pytest", "-v"]
    return run_command(cmd, "所有测试")


def run_fast_tests():
    """运行快速测试（排除慢速测试）"""
    cmd = ["python", "-m", "pytest", "-m", "not slow", "-v"]
    return run_command(cmd, "快速测试")


def run_specific_test(test_path):
    """运行特定测试"""
    cmd = ["python", "-m", "pytest", test_path, "-v"]
    return run_command(cmd, f"特定测试: {test_path}")


def run_coverage_report():
    """运行覆盖率报告"""
    cmd = ["python", "-m", "pytest", "--cov=.", "--cov-report=html", "--cov-report=term-missing"]
    return run_command(cmd, "覆盖率测试")


def check_test_environment():
    """检查测试环境"""
    print("检查测试环境...")
    
    # 检查pytest是否安装
    try:
        import pytest
        print(f"✓ pytest 已安装 (版本: {pytest.__version__})")
    except ImportError:
        print("✗ pytest 未安装")
        return False
    
    # 检查项目依赖
    required_modules = ['cv2', 'numpy', 'app_state', 'constants']
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module} 可用")
        except ImportError:
            print(f"✗ {module} 不可用")
    
    # 检查测试文件
    test_files = [
        'tests/conftest.py',
        'tests/test_ui_handler_original.py',
        'tests/test_state_machine.py',
        'tests/test_ui_components.py',
        'tests/test_event_handlers.py',
        'tests/test_core_logic.py',
        'tests/test_integration.py'
    ]
    
    for test_file in test_files:
        if (project_root / test_file).exists():
            print(f"✓ {test_file} 存在")
        else:
            print(f"✗ {test_file} 不存在")
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行测试套件")
    parser.add_argument("--type", "-t", 
                       choices=["unit", "integration", "performance", "core", "ui", "regression", "all", "fast"],
                       default="fast",
                       help="测试类型")
    parser.add_argument("--file", "-f", 
                       help="运行特定测试文件")
    parser.add_argument("--coverage", "-c", 
                       action="store_true",
                       help="生成覆盖率报告")
    parser.add_argument("--check", 
                       action="store_true",
                       help="检查测试环境")
    
    args = parser.parse_args()
    
    if args.check:
        check_test_environment()
        return
    
    if not check_test_environment():
        print("测试环境检查失败，请先解决依赖问题")
        return
    
    success = True
    
    if args.file:
        success = run_specific_test(args.file)
    elif args.coverage:
        success = run_coverage_report()
    else:
        test_functions = {
            "unit": run_unit_tests,
            "integration": run_integration_tests,
            "performance": run_performance_tests,
            "core": run_core_tests,
            "ui": run_ui_tests,
            "regression": run_regression_tests,
            "all": run_all_tests,
            "fast": run_fast_tests
        }
        
        test_func = test_functions.get(args.type)
        if test_func:
            success = test_func()
        else:
            print(f"未知的测试类型: {args.type}")
            success = False
    
    if success:
        print(f"\n🎉 测试完成！")
    else:
        print(f"\n❌ 测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
